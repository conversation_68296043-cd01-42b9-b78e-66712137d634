using Excalibur.Models;
using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace Excalibur.Services;

public class MartingaleService
{
    private readonly object _lock = new object();
    private readonly ILogger<MartingaleService>? _logger;
    
    // Evento para notificar reset do martingale
    public event EventHandler<MartingaleResetEventArgs>? MartingaleReset;
    private MartingaleGroup? _globalMartingaleGroup;
    private readonly Queue<PendingMartingaleEntry> _pendingEntries = new();
    private readonly Dictionary<string, MartingaleGroup> _individualMartingaleGroups = new();
    private readonly HashSet<string> _processedContractIds = new();
    
    // Nova lógica sequencial
    private bool _hasActiveContract = false;
    private string _activeContractId = string.Empty;
    private int _currentMartingaleLevel = 1;
    private decimal _baseMartingaleStake = 0;
    private decimal _martingaleFactor = 2;
    private int _maxMartingaleLevel = 10;
    
    // Variáveis de compatibilidade
    private bool _lastResult = false;
    private DateTime _lastProcessTime = DateTime.MinValue;
    private bool _isProcessingEntry = false;
    private int _lossCount = 0;
    private bool _needsMartingale = false;
    #pragma warning disable CS0414 // Field is assigned but its value is never used
    private bool _waitingForContractExpiry = false;
    #pragma warning restore CS0414
    #pragma warning disable CS0414 // Field is assigned but its value is never used
    private Func<decimal, Task<string>>? _pendingPurchaseFunction;
    #pragma warning restore CS0414
    private string _pendingGroupId = string.Empty;
    
    // Estrutura para resultado de execução
    private struct ExecutionResult
    {
        public bool Success { get; set; }
        public string ContractId { get; set; }
        public string Message { get; set; }
    }

    public MartingaleService(ILogger<MartingaleService>? logger = null)
    {
        _logger = logger;
    }

    public class PendingMartingaleEntry
    {
        public string Id { get; set; } = string.Empty;
        public decimal BaseStake { get; set; }
        public Func<decimal, Task> ExecutePurchase { get; set; } = null!;
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public string GroupId { get; set; } = string.Empty;
        public bool InheritsMartingale { get; set; } = false;
        public int MartingaleLevel { get; set; } = 1;
    }

    public MartingaleGroup GetOrCreateGlobalMartingaleGroup(decimal baseStake, decimal factor, int maxLevel)
    {
        lock (_lock)
        {
            if (_globalMartingaleGroup == null || !_globalMartingaleGroup.IsActive)
            {
                _globalMartingaleGroup = new MartingaleGroup
                {
                    Id = "global_martingale",
                    BaseStake = baseStake,
                    Factor = factor,
                    MaxLevel = maxLevel,
                    CurrentLevel = 1,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
            }
            return _globalMartingaleGroup;
        }
    }

    public MartingaleGroup? GetGlobalMartingaleGroup()
    {
        lock (_lock)
        {
            return _globalMartingaleGroup;
        }
    }

    public decimal GetCurrentStakeForNewEntry()
    {
        lock (_lock)
        {
            // CORREÇÃO: Usar as variáveis internas em vez do grupo global
            if (_baseMartingaleStake <= 0)
                return 0;

            // Calcular stake baseado no nível atual
            if (_needsMartingale && _currentMartingaleLevel > 1)
            {
                var powerResult = Math.Pow((double)_martingaleFactor, _currentMartingaleLevel - 1);
                return _baseMartingaleStake * (decimal)powerResult;
            }
            else
            {
                return _baseMartingaleStake;
            }
        }
    }

    public int GetCurrentLevel()
    {
        lock (_lock)
        {
            return _globalMartingaleGroup?.CurrentLevel ?? 1;
        }
    }

    public int GetCurrentMartingaleLevel()
    {
        lock (_lock)
        {
            return _currentMartingaleLevel;
        }
    }

    public bool CanAcceptNewEntry()
    {
        lock (_lock)
        {
            return !_hasActiveContract;
        }
    }

    public void InitializeMartingaleSettings(decimal baseStake, decimal factor, int maxLevel)
    {
        lock (_lock)
        {
            // CORREÇÃO: Detectar primeira inicialização baseado em múltiplos fatores
            // Não apenas no baseStake, mas também se nunca foi inicializado antes
            bool isFirstInitialization = _baseMartingaleStake <= 0 && _currentMartingaleLevel <= 1 && _lossCount == 0;
            bool valuesChanged = _baseMartingaleStake != baseStake || _martingaleFactor != factor || _maxMartingaleLevel != maxLevel;

            // Log do estado atual antes da inicialização
            _logger?.LogInformation("[MARTINGALE] ⚙️ InitializeMartingaleSettings chamado:");
            _logger?.LogInformation("[MARTINGALE] ⚙️ Parâmetros recebidos - BaseStake: {BaseStake}, Factor: {Factor}, MaxLevel: {MaxLevel}", baseStake, factor, maxLevel);
            _logger?.LogInformation("[MARTINGALE] ⚙️ Estado atual - BaseStake: {CurrentBaseStake}, Factor: {CurrentFactor}, MaxLevel: {CurrentMaxLevel}, Level: {CurrentLevel}",
                _baseMartingaleStake, _martingaleFactor, _maxMartingaleLevel, _currentMartingaleLevel);
            _logger?.LogInformation("[MARTINGALE] ⚙️ Estado martingale - LossCount: {LossCount}, NeedsMartingale: {NeedsMartingale}", _lossCount, _needsMartingale);
            _logger?.LogInformation("[MARTINGALE] ⚙️ Análise inicialização - IsFirst: {IsFirst}, ValuesChanged: {ValuesChanged}", isFirstInitialization, valuesChanged);

            if (!isFirstInitialization && !valuesChanged)
            {
                _logger?.LogInformation("[MARTINGALE] ⚡ Sistema já inicializado com os mesmos valores, mantendo configuração atual");
                return;
            }

            // Validar parâmetros antes de atualizar
            if (baseStake <= 0)
            {
                _logger?.LogError("[MARTINGALE] ❌ Base stake inválido: {BaseStake}. Ignorando inicialização.", baseStake);
                return;
            }

            if (factor <= 1)
            {
                _logger?.LogError("[MARTINGALE] ❌ Factor inválido: {Factor}. Deve ser maior que 1. Ignorando inicialização.", factor);
                return;
            }

            if (maxLevel < 1)
            {
                _logger?.LogError("[MARTINGALE] ❌ Max level inválido: {MaxLevel}. Deve ser pelo menos 1. Ignorando inicialização.", maxLevel);
                return;
            }

            _baseMartingaleStake = baseStake;
            _martingaleFactor = factor;
            _maxMartingaleLevel = maxLevel;

            string action = isFirstInitialization ? "inicializadas" : "atualizadas";
            _logger?.LogInformation("[MARTINGALE] ⚙️ Configurações {Action}:", action);
            _logger?.LogInformation("[MARTINGALE] ⚙️ Base Stake: {BaseStake}", _baseMartingaleStake);
            _logger?.LogInformation("[MARTINGALE] ⚙️ Factor: {Factor}", _martingaleFactor);
            _logger?.LogInformation("[MARTINGALE] ⚙️ Max Level: {MaxLevel}", _maxMartingaleLevel);

            // CORREÇÃO: Só resetar o estado do martingale se for REALMENTE a primeira inicialização
            if (isFirstInitialization)
            {
                _currentMartingaleLevel = 1;
                _lossCount = 0;
                _needsMartingale = false;
                _logger?.LogInformation("[MARTINGALE] ⚙️ Sistema inicializado com nível 1, 0 perdas e needsMartingale=false");
            }
            else
            {
                // Se não é primeira inicialização, PRESERVAR o estado atual do martingale
                _logger?.LogInformation("[MARTINGALE] ⚙️ Atualizando configurações mas PRESERVANDO estado do martingale:");
                _logger?.LogInformation("[MARTINGALE] ⚙️ Mantendo - Level: {Level}, LossCount: {LossCount}, NeedsMartingale: {NeedsMartingale}",
                    _currentMartingaleLevel, _lossCount, _needsMartingale);

                // Se não é primeira inicialização, verificar se o nível atual ainda é válido
                if (_currentMartingaleLevel > _maxMartingaleLevel)
                {
                    _logger?.LogWarning("[MARTINGALE] ⚠️ Nível atual ({CurrentLevel}) excede novo máximo ({MaxLevel}), ajustando para máximo", _currentMartingaleLevel, _maxMartingaleLevel);
                    _currentMartingaleLevel = _maxMartingaleLevel;
                }
            }

            _logger?.LogInformation("[MARTINGALE] ⚙️ Estado final após inicialização - Level: {Level}, LossCount: {LossCount}, NeedsMartingale: {NeedsMartingale}",
                _currentMartingaleLevel, _lossCount, _needsMartingale);
        }
    }

    public async Task<bool> TryExecuteEntryAsync(decimal baseStake, Func<decimal, Task<string>> executePurchase, string groupId = "default")
    {
        decimal currentStake;
        string timestamp;
        
        lock (_lock)
        {
            timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            
            _logger?.LogInformation("[MARTINGALE] 📨 [{Timestamp}] TryExecuteEntryAsync chamado!", timestamp);
            _logger?.LogInformation("[MARTINGALE] 📨 [{Timestamp}] BaseStake: {BaseStake}, GroupId: {GroupId}", timestamp, baseStake, groupId);
            _logger?.LogInformation("[MARTINGALE] 📨 [{Timestamp}] Has Active Contract: {HasActiveContract}", timestamp, _hasActiveContract);
            _logger?.LogInformation("[MARTINGALE] 📨 [{Timestamp}] Current Martingale Level: {CurrentMartingaleLevel}", timestamp, _currentMartingaleLevel);
            _logger?.LogInformation("[MARTINGALE] 📨 [{Timestamp}] Loss Count: {LossCount}", timestamp, _lossCount);
            _logger?.LogInformation("[MARTINGALE] 📨 [{Timestamp}] Needs Martingale: {NeedsMartingale}", timestamp, _needsMartingale);
            _logger?.LogInformation("[MARTINGALE] 📨 [{Timestamp}] Base Martingale Stake: {BaseMartingaleStake}", timestamp, _baseMartingaleStake);
            _logger?.LogInformation("[MARTINGALE] 📨 [{Timestamp}] Martingale Factor: {MartingaleFactor}", timestamp, _martingaleFactor);
            _logger?.LogInformation("[MARTINGALE] 📨 [{Timestamp}] Max Martingale Level: {MaxMartingaleLevel}", timestamp, _maxMartingaleLevel);
            
            // VERIFICAÇÃO DE SEGURANÇA: Garantir que _baseMartingaleStake não foi corrompido
            if (_baseMartingaleStake <= 0)
            {
                _logger?.LogWarning("[MARTINGALE] ⚠️ [{Timestamp}] Base stake zerado! Stored: {StoredStake}, Received: {ReceivedStake}. Corrigindo...", timestamp, _baseMartingaleStake, baseStake);
                _baseMartingaleStake = baseStake;
            }
            else if (Math.Abs(_baseMartingaleStake - baseStake) > 0.01m)
            {
                // CORREÇÃO: NÃO sobrescrever se já foi inicializado corretamente
                // O baseStake recebido pode ser o stake da última compra (incorreto)
                _logger?.LogWarning("[MARTINGALE] ⚠️ [{Timestamp}] Base stake diferente detectado! Stored: {StoredStake}, Received: {ReceivedStake}. MANTENDO valor armazenado (correto).", timestamp, _baseMartingaleStake, baseStake);
            }

            // VERIFICAÇÃO ADICIONAL: Garantir que o nível do martingale está correto
            if (_currentMartingaleLevel < 1)
            {
                _logger?.LogWarning("[MARTINGALE] ⚠️ [{Timestamp}] Nível do martingale inválido ({Level}), corrigindo para 1", timestamp, _currentMartingaleLevel);
                _currentMartingaleLevel = 1;
            }

            if (_currentMartingaleLevel > _maxMartingaleLevel)
            {
                _logger?.LogWarning("[MARTINGALE] ⚠️ [{Timestamp}] Nível do martingale ({Level}) excede máximo ({MaxLevel}), corrigindo para máximo", timestamp, _currentMartingaleLevel, _maxMartingaleLevel);
                _currentMartingaleLevel = _maxMartingaleLevel;
            }
            
            // NOVA LÓGICA: Verificar se pode aceitar nova entrada
            if (_hasActiveContract)
            {
                _logger?.LogInformation("[MARTINGALE] ❌ [{Timestamp}] ENTRADA REJEITADA! Contrato ativo em andamento: {ActiveContractId}", timestamp, _activeContractId);
                _logger?.LogInformation("[MARTINGALE] ⏳ [{Timestamp}] Armazenando entrada para execução após expiração do contrato", timestamp);
                
                // Armazenar entrada para execução após expiração
                // _pendingPurchaseFunction = executePurchase; // This line was removed
                // _pendingGroupId = groupId; // This line was removed
                
                return false;
            }

            // Calcular stake baseado no nível de martingale atual
            try
            {
                if (_currentMartingaleLevel > 1)
                {
                    // Log detalhado do cálculo com validação da sequência
                    _logger?.LogInformation("[MARTINGALE] 📊 [{Timestamp}] === CÁLCULO MARTINGALE ===", timestamp);
                    _logger?.LogInformation("[MARTINGALE] 📊 [{Timestamp}] BaseStake: {BaseStake}", timestamp, _baseMartingaleStake);
                    _logger?.LogInformation("[MARTINGALE] 📊 [{Timestamp}] Factor: {Factor}", timestamp, _martingaleFactor);
                    _logger?.LogInformation("[MARTINGALE] 📊 [{Timestamp}] Level: {Level}", timestamp, _currentMartingaleLevel);
                    _logger?.LogInformation("[MARTINGALE] 📊 [{Timestamp}] Potência esperada: {Factor}^{Power}", timestamp, _martingaleFactor, _currentMartingaleLevel - 1);
                    
                    var powerResult = Math.Pow((double)_martingaleFactor, _currentMartingaleLevel - 1);
                    _logger?.LogInformation("[MARTINGALE] 📊 [{Timestamp}] Resultado da potência: {PowerResult}", timestamp, powerResult);
                    
                    currentStake = _baseMartingaleStake * (decimal)powerResult;
                    _logger?.LogInformation("[MARTINGALE] 📊 [{Timestamp}] Stake calculado: {BaseStake} × {PowerResult} = {CurrentStake}", timestamp, _baseMartingaleStake, powerResult, currentStake);
                    
                    // Validar sequência esperada para debug
                    switch (_currentMartingaleLevel)
                    {
                        case 2:
                            var expected2 = _baseMartingaleStake * _martingaleFactor;
                            _logger?.LogInformation("[MARTINGALE] 📊 [{Timestamp}] Nível 2 - Esperado: {Expected}, Calculado: {Calculated}", timestamp, expected2, currentStake);
                            break;
                        case 3:
                            var expected3 = _baseMartingaleStake * _martingaleFactor * _martingaleFactor;
                            _logger?.LogInformation("[MARTINGALE] 📊 [{Timestamp}] Nível 3 - Esperado: {Expected}, Calculado: {Calculated}", timestamp, expected3, currentStake);
                            break;
                    }
                    
                    // Aplicar limite máximo de stake para evitar valores excessivos
                    const decimal MAX_STAKE = 1000m; // Limite máximo de 1000 USD
                    if (currentStake > MAX_STAKE)
                    {
                        _logger?.LogWarning("[MARTINGALE] ⚠️ [{Timestamp}] Stake calculado ({CalculatedStake}) excede limite máximo ({MaxStake}). Limitando valor.", timestamp, currentStake, MAX_STAKE);
                        currentStake = MAX_STAKE;
                    }
                    
                    // Arredondar para 2 casas decimais para evitar erro da API
                    currentStake = Math.Round(currentStake, 2);
                    _logger?.LogInformation("[MARTINGALE] 🔥 [{Timestamp}] USANDO MARTINGALE! Nível: {CurrentMartingaleLevel}, Stake: {CurrentStake} (Factor: {Factor})", timestamp, _currentMartingaleLevel, currentStake, _martingaleFactor);
                    _logger?.LogInformation("[MARTINGALE] 🔥 [{Timestamp}] Perdas consecutivas: {LossCount}", timestamp, _lossCount);
                }
                else
                {
                    currentStake = _baseMartingaleStake;
                    _logger?.LogInformation("[MARTINGALE] ✨ [{Timestamp}] Entrada normal, Stake: {CurrentStake} (Nível 1 - sem perdas ou após ganho)", timestamp, currentStake);
                }
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "[MARTINGALE] ❌ [{Timestamp}] ERRO no cálculo do stake! Usando baseStake como fallback", timestamp);
                currentStake = baseStake;
            }
        }

        // Log de confirmação antes de executar compra
        _logger?.LogInformation("[MARTINGALE] 🎯 [{0}] Preparando para executar compra com stake final: {1}", DateTime.Now.ToString("HH:mm:ss.fff"), currentStake);

        // Executar compra fora do lock
        try
        {
            timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            _logger?.LogInformation("[MARTINGALE] 🚀 [{Timestamp}] Executando compra com stake {CurrentStake}", timestamp, currentStake);
            
            string contractId = await executePurchase(currentStake);
            
            lock (_lock)
            {
                if (!string.IsNullOrEmpty(contractId))
                {
                    // CORREÇÃO: Validar se é um contrato real antes de registrar como ativo
                    bool isRealContract = contractId.All(char.IsDigit);
                    
                    if (isRealContract)
                    {
                        _hasActiveContract = true;
                        _activeContractId = contractId;
                        
                        _logger?.LogInformation("[MARTINGALE] ✅ [{Timestamp}] Compra REAL executada! Contract ID: {ContractId}", timestamp, contractId);
                        _logger?.LogInformation("[MARTINGALE] ⏳ [{Timestamp}] Aguardando expiração do contrato real...", timestamp);
                        return true;
                    }
                    else
                    {
                        _logger?.LogWarning("[MARTINGALE] ⚠️ [{Timestamp}] Contract ID {ContractId} é simulação - NÃO será registrado como ativo no martingale", timestamp, contractId);
                        _logger?.LogInformation("[MARTINGALE] 💡 [{Timestamp}] Para martingale funcionar, execute compras REAIS (não simulações)", timestamp);
                        return false;
                    }
                }
                else
                {
                    _logger?.LogInformation("[MARTINGALE] ❌ [{Timestamp}] Falha na execução da compra - Contract ID vazio", timestamp);
                    return false;
                }
            }
        }
        catch (Exception ex)
        {
            timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            _logger?.LogError(ex, "[MARTINGALE] ❌ [{Timestamp}] Erro ao executar compra: {Message}", timestamp, ex.Message);
            return false;
        }
    }

    // Nova função para configurar verificação de critérios de simulação
    public void SetSimulationCriteriaChecker(Func<Task<bool>> checker)
    {
        lock (_lock)
        {
            // _checkSimulationCriteria = checker; // This line was removed
            _logger?.LogInformation("[MARTINGALE] 🔍 Verificador de critérios de simulação configurado");
        }
    }

    private void ProcessNextEntry()
    {
        lock (_lock)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            _logger?.LogInformation("[MARTINGALE] 🔄 [{Timestamp}] ProcessNextEntry chamado", timestamp);
            // _logger?.LogInformation("[MARTINGALE] 🔄 [{Timestamp}] Fila count: {PendingCount}, _isProcessingEntry: {IsProcessingEntry}", timestamp, _pendingEntries.Count, _isProcessingEntry); // This line was removed
            
            if (_pendingEntries.Count == 0)
            {
                _logger?.LogInformation("[MARTINGALE] 🔄 [{Timestamp}] Saindo - Fila vazia ou já processando", timestamp);
                return;
            }

            // _isProcessingEntry = true; // This line was removed
            var entry = _pendingEntries.Dequeue();
            
            _logger?.LogInformation("[MARTINGALE] 🔄 [{Timestamp}] Processando entrada {EntryId}", timestamp, entry.Id);
            _logger?.LogInformation("[MARTINGALE] 🔄 [{Timestamp}] Entry.InheritsMartingale: {InheritsMartingale}", timestamp, entry.InheritsMartingale);
            _logger?.LogInformation("[MARTINGALE] 🔄 [{Timestamp}] Entry.MartingaleLevel: {MartingaleLevel}", timestamp, entry.MartingaleLevel);
            
            // Calcular stake baseado no martingale individual
            decimal currentStake;
            if (entry.InheritsMartingale)
            {
                // Criar ou obter grupo individual para esta entrada
                var martingaleGroup = GetOrCreateIndividualMartingaleGroup(entry.GroupId, entry.BaseStake, 2, 10);
                martingaleGroup.CurrentLevel = entry.MartingaleLevel;
                currentStake = martingaleGroup.CalculateCurrentStake();
                
                _logger?.LogInformation("[MARTINGALE] 🔥 [{Timestamp}] Entrada {EntryId} herdando martingale nível {MartingaleLevel}, stake: {CurrentStake}", timestamp, entry.Id, entry.MartingaleLevel, currentStake);
            }
            else
            {
                // Usar stake base normal
                currentStake = entry.BaseStake;
                _logger?.LogInformation("[MARTINGALE] ✨ [{Timestamp}] Entrada {EntryId} stake normal: {CurrentStake}", timestamp, entry.Id, currentStake);
            }

            // Executar compra de forma assíncrona
            Task.Run(async () =>
            {
                try
                {
                    _logger?.LogInformation("[MARTINGALE] 🚀 [{Timestamp}] Executando compra para entrada {EntryId} com stake {CurrentStake}", timestamp, entry.Id, currentStake);
                    await entry.ExecutePurchase(currentStake);
                    _logger?.LogInformation("[MARTINGALE] ✅ [{Timestamp}] Compra executada com sucesso para entrada {EntryId}", timestamp, entry.Id);
                }
                catch (Exception ex)
                {
                    _logger?.LogError(ex, "[MARTINGALE] ❌ [{Timestamp}] Erro ao executar compra para entrada {EntryId}: {Message}", timestamp, entry.Id, ex.Message);
                    
                    // Em caso de erro, marcar que não está mais processando para não travar o sistema
                    lock (_lock)
                    {
                        _logger?.LogInformation("[MARTINGALE] 🔄 [{Timestamp}] Erro processado", timestamp);
                        ProcessNextEntry();
                    }
                }
            });
        }
    }

    public void ProcessContractResult(bool isWin, string contractId = "")
    {
        lock (_lock)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");

            _logger?.LogInformation("[MARTINGALE] 🎯 [{Timestamp}] ProcessContractResult chamado!", timestamp);
            _logger?.LogInformation("[MARTINGALE] 🎯 [{Timestamp}] IsWin: {IsWin}, ContractId: {ContractId}", timestamp, isWin, contractId);
            _logger?.LogInformation("[MARTINGALE] 🎯 [{Timestamp}] Estado antes: Level={Level}, LossCount={LossCount}", timestamp, _currentMartingaleLevel, _lossCount);

            // Atualizar variáveis legadas para compatibilidade
            _lastResult = isWin;
            _lastProcessTime = DateTime.Now;

            if (isWin)
            {
                // CORREÇÃO: Resetar TANTO o contador de perdas QUANTO o nível do martingale
                _lossCount = 0;
                _currentMartingaleLevel = 1; // IMPORTANTE: Resetar nível para 1 após vitória
                _needsMartingale = false;
                _logger?.LogInformation("[MARTINGALE] ✅ [{Timestamp}] VITÓRIA! Reset completo: Level=1, LossCount=0", timestamp);

                // Disparar evento de reset do martingale
                var resetArgs = new MartingaleResetEventArgs
                {
                    OriginalStake = _baseMartingaleStake,
                    ResetReason = "Victory"
                };
                MartingaleReset?.Invoke(this, resetArgs);
                _logger?.LogInformation("[MARTINGALE] 🔔 [{Timestamp}] Evento MartingaleReset disparado por vitória", timestamp);
            }
            else
            {
                _lossCount++;
                _needsMartingale = true;

                // CORREÇÃO: Incrementar nível do martingale apenas se não atingiu o máximo
                if (_currentMartingaleLevel < _maxMartingaleLevel)
                {
                    _currentMartingaleLevel++;
                    _logger?.LogInformation("[MARTINGALE] ❌ [{Timestamp}] PERDA #{LossCount}! Nível aumentado para {Level}", timestamp, _lossCount, _currentMartingaleLevel);
                }
                else
                {
                    _logger?.LogWarning("[MARTINGALE] 🔴 [{Timestamp}] PERDA #{LossCount}! Nível máximo atingido ({MaxLevel}), mantendo nível atual", timestamp, _lossCount, _maxMartingaleLevel);
                }
            }

            _logger?.LogInformation("[MARTINGALE] 🎯 [{Timestamp}] Estado após: Level={Level}, LossCount={LossCount}, NeedsMartingale={NeedsMartingale}",
                timestamp, _currentMartingaleLevel, _lossCount, _needsMartingale);

            // Marcar que não está mais processando e processar próxima entrada
            _isProcessingEntry = false;
            ProcessNextEntry();

            // Nota: ProcessContractExpiration deve ser chamado separadamente
            // quando necessário, não automaticamente aqui para evitar processamento duplo
        }
    }

    // Registrar um contrato ativo no sistema
    public void RegisterActiveContract(string contractId)
    {
        lock (_lock)
        {
            // CORREÇÃO: Permitir que contratos reais (RefId numérico) substituam contratos de simulação
            bool isRealContract = contractId.All(char.IsDigit);
            bool hasActiveContract = _hasActiveContract && !string.IsNullOrEmpty(_activeContractId);
            bool currentIsSimulation = hasActiveContract && !_activeContractId.All(char.IsDigit);
            
            if (hasActiveContract)
            {
                if (isRealContract && currentIsSimulation)
                {
                    _logger?.LogInformation("[MARTINGALE] 🔄 Substituindo contrato de simulação {OldContract} por compra real {NewContract}", 
                        _activeContractId, contractId);
                    _activeContractId = contractId;
                    _hasActiveContract = true;
                }
                else if (isRealContract && !currentIsSimulation)
                {
                    _logger?.LogWarning("[MARTINGALE] ⚠️ Tentativa de registrar contrato real {NewContract} mas já há uma compra real ativa: {ActiveContract}", 
                        contractId, _activeContractId);
                }
                else if (!isRealContract && !currentIsSimulation)
                {
                    _logger?.LogWarning("[MARTINGALE] ⚠️ Tentativa de registrar simulação {NewContract} mas já há compra real ativa: {ActiveContract}", 
                        contractId, _activeContractId);
                }
                else
                {
                    _logger?.LogInformation("[MARTINGALE] 🔄 Substituindo simulação {OldContract} por nova simulação {NewContract}", 
                        _activeContractId, contractId);
                    _activeContractId = contractId;
                    _hasActiveContract = true;
                }
            }
            else
            {
                _activeContractId = contractId;
                _hasActiveContract = true;
                string contractType = isRealContract ? "COMPRA REAL" : "SIMULAÇÃO";
                _logger?.LogInformation("[MARTINGALE] ✅ Contrato {ContractType} registrado como ativo: {ContractId}", contractType, contractId);
            }
        }
    }

    // Verificar se um contrato específico já foi processado
    public bool HasContractBeenProcessed(string contractId)
    {
        lock (_lock)
        {
            return _processedContractIds.Contains(contractId);
        }
    }

    // Processar a expiração de um contrato (nova lógica sequencial)
    public void ProcessContractExpiration(string contractId, bool isWin)
    {
        lock (_lock)
        {
            _logger?.LogInformation("[MARTINGALE] 🎯 ProcessContractExpiration: {ContractId}, IsWin: {IsWin}", contractId, isWin);
            
            // CORREÇÃO: Verificar se já foi processado para evitar valores repetidos do martingale
            if (_processedContractIds.Contains(contractId))
            {
                _logger?.LogWarning("[MARTINGALE] ⚠️ Contrato {ContractId} já foi processado! Ignorando resultado duplicado para evitar valores repetidos do martingale.", contractId);
                return;
            }
            
            // Marcar como processado ANTES de qualquer processamento
            _processedContractIds.Add(contractId);
            _logger?.LogInformation("[MARTINGALE] 📝 Contrato {ContractId} marcado como processado", contractId);
            
            // CORREÇÃO: Verificar se é o contrato ativo atual OU se não temos contrato ativo (para processar resultados pendentes)
            bool shouldProcess = false;
            bool isRealContract = contractId.All(char.IsDigit);
            
            if (_activeContractId == contractId)
            {
                shouldProcess = true;
                _logger?.LogInformation("[MARTINGALE] 🎯 Processando contrato ativo: {ContractId}", contractId);
            }
            else if (string.IsNullOrEmpty(_activeContractId) && !_hasActiveContract && isRealContract)
            {
                // Se não há contrato ativo e é um contrato real, aceitar para processar
                shouldProcess = true;
                _logger?.LogInformation("[MARTINGALE] 🎯 Nenhum contrato ativo registrado, processando contrato real: {ContractId}", contractId);
            }
            else if (!isRealContract)
            {
                _logger?.LogInformation("[MARTINGALE] 🔍 Contrato {ContractId} é simulação - ignorando para martingale", contractId);
                return;
            }
            else
            {
                _logger?.LogWarning("[MARTINGALE] ⚠️ Contrato {ContractId} não é o ativo atual ({ActiveContractId}) - IGNORANDO", contractId, _activeContractId);
                return;
            }
            
            if (shouldProcess)
            {
                // Marcar como não ativo
                _hasActiveContract = false;
                _activeContractId = string.Empty;
                
                // CORREÇÃO: Usar ProcessContractResult para manter consistência e evitar duplicação
                _logger?.LogInformation("[MARTINGALE] 🔄 Delegando processamento para ProcessContractResult para manter consistência");

                // Chamar ProcessContractResult que já tem toda a lógica correta
                ProcessContractResult(isWin, contractId);

                // Log adicional específico para ProcessContractExpiration
                if (isWin)
                {
                    _logger?.LogInformation("[MARTINGALE] ✅ Contrato {ContractId} processado com VITÓRIA via ProcessContractExpiration", contractId);
                }
                else
                {
                    _logger?.LogInformation("[MARTINGALE] ❌ Contrato {ContractId} processado com PERDA via ProcessContractExpiration", contractId);

                    // Verificar se base stake está zerado e corrigir
                    if (_baseMartingaleStake <= 0)
                    {
                        _logger?.LogError("[MARTINGALE] ❌ Base stake zerado! Não é possível calcular martingale. BaseStake: {BaseStake}", _baseMartingaleStake);
                        return;
                    }

                    // Log do próximo stake calculado
                    if (_currentMartingaleLevel <= _maxMartingaleLevel)
                    {
                        var nextStake = _baseMartingaleStake * (decimal)Math.Pow((double)_martingaleFactor, _currentMartingaleLevel - 1);
                        _logger?.LogInformation("[MARTINGALE] 📈 Próxima entrada será com stake: {NextStake} (Base: {BaseStake} × {Factor}^{Power})",
                            nextStake, _baseMartingaleStake, _martingaleFactor, _currentMartingaleLevel - 1);
                    }
                }
                
                // Processar entradas pendentes
                _ = Task.Run(async () => await ProcessPendingEntries());
            }
        }
    }

    // Processar entradas pendentes (nova lógica sequencial)
    private async Task ProcessPendingEntries()
    {
        _logger?.LogInformation("[MARTINGALE] 🔄 Processando entradas pendentes. Count: {Count}", _pendingEntries.Count);
        
        while (_pendingEntries.Count > 0 && !_hasActiveContract)
        {
            var pendingEntry = _pendingEntries.Dequeue();
            _logger?.LogInformation("[MARTINGALE] 🔄 Executando entrada pendente: {GroupId}", pendingEntry.GroupId);
            
            // Adaptar ExecutePurchase para retornar string
            Func<decimal, Task<string>> adaptedExecutePurchase = async (stake) =>
            {
                await pendingEntry.ExecutePurchase(stake);
                return _activeContractId; // Retornar o ID do contrato ativo
            };
            
            // Executar a entrada pendente
            var result = await TryExecuteEntryAsync(pendingEntry.BaseStake, adaptedExecutePurchase, pendingEntry.GroupId);
            
            if (result)
            {
                _logger?.LogInformation("[MARTINGALE] ✅ Entrada pendente executada com sucesso");
                break; // Sair do loop após executar uma entrada
            }
            else
            {
                _logger?.LogError("[MARTINGALE] ❌ Falha ao executar entrada pendente");
            }
        }
    }

    public void ResetMartingale()
    {
        lock (_lock)
        {
            // Reset sistema novo
            _hasActiveContract = false;
            _activeContractId = string.Empty;
            _currentMartingaleLevel = 1;
            _waitingForContractExpiry = false;
            
            // Limpar entradas pendentes
            _pendingPurchaseFunction = null;
            _pendingGroupId = string.Empty;
            
            // Reset sistema legado
            _globalMartingaleGroup?.Reset();
            _individualMartingaleGroups.Clear();
            _pendingEntries.Clear();
            _processedContractIds.Clear();
            _isProcessingEntry = false;
            _lossCount = 0;
            _needsMartingale = false;
            
            _logger?.LogInformation("[MARTINGALE] 🧹 Sistema resetado!");
            _logger?.LogInformation("[MARTINGALE] 🧹   - Nível martingale: {CurrentMartingaleLevel}", _currentMartingaleLevel);
            _logger?.LogInformation("[MARTINGALE] 🧹   - Contrato ativo: {HasActiveContract}", _hasActiveContract);
            _logger?.LogInformation("[MARTINGALE] 🧹   - Contratos processados limpos: {ProcessedCount}", _processedContractIds.Count);
        }
    }

    public bool HasPendingEntries()
    {
        lock (_lock)
        {
            return _pendingEntries.Count > 0;
        }
    }

    public int GetPendingEntriesCount()
    {
        lock (_lock)
        {
            return _pendingEntries.Count;
        }
    }

    public MartingaleGroup GetOrCreateIndividualMartingaleGroup(string groupId, decimal baseStake, decimal factor, int maxLevel)
    {
        lock (_lock)
        {
            if (!_individualMartingaleGroups.ContainsKey(groupId))
            {
                _individualMartingaleGroups[groupId] = new MartingaleGroup
                {
                    Id = groupId,
                    BaseStake = baseStake,
                    Factor = factor,
                    MaxLevel = maxLevel,
                    CurrentLevel = 1,
                    IsActive = true,
                    CreatedAt = DateTime.Now
                };
            }
            return _individualMartingaleGroups[groupId];
        }
    }

    public int GetCurrentLossCount()
    {
        lock (_lock)
        {
            return _lossCount;
        }
    }

    public bool NeedsMartingale()
    {
        lock (_lock)
        {
            return _needsMartingale;
        }
    }

    public bool HasActiveContract()
    {
        lock (_lock)
        {
            return _hasActiveContract;
        }
    }

    public string GetActiveContractId()
    {
        lock (_lock)
        {
            return _activeContractId;
        }
    }

    // Método para diagnóstico completo do estado do sistema
    public void LogSystemState(string context = "")
    {
        lock (_lock)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
            var contextStr = string.IsNullOrEmpty(context) ? "" : $" [{context}]";

            _logger?.LogInformation("[MARTINGALE] 🔍 [{Timestamp}] ESTADO DO SISTEMA{Context}:", timestamp, contextStr);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Base Stake: {BaseStake}", _baseMartingaleStake);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Factor: {Factor}", _martingaleFactor);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Max Level: {MaxLevel}", _maxMartingaleLevel);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Current Level: {CurrentLevel}", _currentMartingaleLevel);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Loss Count: {LossCount}", _lossCount);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Needs Martingale: {NeedsMartingale}", _needsMartingale);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Has Active Contract: {HasActiveContract}", _hasActiveContract);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Active Contract ID: {ActiveContractId}", _activeContractId ?? "null");
            _logger?.LogInformation("[MARTINGALE] 🔍   - Is Processing Entry: {IsProcessingEntry}", _isProcessingEntry);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Pending Entries: {PendingCount}", _pendingEntries.Count);
            _logger?.LogInformation("[MARTINGALE] 🔍   - Processed Contracts: {ProcessedCount}", _processedContractIds.Count);

            // Calcular stake atual
            if (_baseMartingaleStake > 0 && _currentMartingaleLevel >= 1)
            {
                var currentStake = _baseMartingaleStake * (decimal)Math.Pow((double)_martingaleFactor, _currentMartingaleLevel - 1);
                _logger?.LogInformation("[MARTINGALE] 🔍   - Current Calculated Stake: {CurrentStake}", currentStake);
            }
            else
            {
                _logger?.LogInformation("[MARTINGALE] 🔍   - Current Calculated Stake: ERRO (BaseStake={BaseStake}, Level={Level})", _baseMartingaleStake, _currentMartingaleLevel);
            }
        }
    }

    // Método para atualizar o ID do contrato ativo (quando ProposalId é trocado por ContractId)
    public void UpdateActiveContractId(string newContractId)
    {
        lock (_lock)
        {
            var oldId = _activeContractId;
            _activeContractId = newContractId;
            
            _logger?.LogInformation("[MARTINGALE] 🔄 Active Contract ID atualizado: {OldId} -> {NewId}", oldId, newContractId);
            
            // LOG INFORMATIVO para aparecer no arquivo log.txt
            _logger?.LogInformation("[MARTINGALE] 🔄 INFO: Active Contract ID atualizado: {OldId} -> {NewId}", oldId, newContractId);
        }
    }

    // Nova função para verificar se há entrada pendente
    public bool HasPendingEntry()
    {
        lock (_lock)
        {
            return _pendingPurchaseFunction != null;
        }
    }

    // Nova função para cancelar entrada pendente
    public void CancelPendingEntry()
    {
        lock (_lock)
        {
            if (_pendingPurchaseFunction != null)
            {
                _logger?.LogInformation("[MARTINGALE] 🚮 Cancelando entrada pendente para grupo: {GroupId}", _pendingGroupId);
                _pendingPurchaseFunction = null;
                _pendingGroupId = string.Empty;
            }
        }
    }

    // Nova função para obter estado completo do sistema
    public void GetMartingaleSystemState(out int martingaleLevel, out bool hasActiveContract, out string activeContractId, out bool hasPendingEntry, out string pendingGroupId)
    {
        lock (_lock)
        {
            martingaleLevel = _currentMartingaleLevel;
            hasActiveContract = _hasActiveContract;
            activeContractId = _activeContractId;
            hasPendingEntry = _pendingPurchaseFunction != null;
            pendingGroupId = _pendingGroupId;
        }
    }

    // Métodos para verificar configurações atuais
    public decimal GetBaseMartingaleStake()
    {
        lock (_lock)
        {
            return _baseMartingaleStake;
        }
    }

    public decimal GetMartingaleFactor()
    {
        lock (_lock)
        {
            return _martingaleFactor;
        }
    }

    // CORREÇÃO: Método para verificar se um contrato foi processado
    public bool WasContractProcessed(string contractId)
    {
        lock (_lock)
        {
            bool wasProcessed = _processedContractIds.Contains(contractId);
            _logger?.LogInformation("[MARTINGALE] 🔍 WasContractProcessed: {ContractId} = {WasProcessed}", contractId, wasProcessed);
            return wasProcessed;
        }
    }

    // CORREÇÃO: Método para corrigir resultado quando API fornece dados diferentes
    public void CorrectContractResult(string contractId, bool correctedIsWin)
    {
        lock (_lock)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss.fff");

            _logger?.LogInformation("[MARTINGALE] 🔄 [{Timestamp}] CorrectContractResult chamado para {ContractId}, CorrectedIsWin: {CorrectedIsWin}",
                timestamp, contractId, correctedIsWin);

            // Verificar se este contrato foi processado recentemente
            if (!_processedContractIds.Contains(contractId))
            {
                _logger?.LogWarning("[MARTINGALE] ⚠️ [{Timestamp}] Contrato {ContractId} não foi processado anteriormente, ignorando correção",
                    timestamp, contractId);
                return;
            }

            // Se o último resultado processado foi diferente, corrigir
            if (_lastResult != correctedIsWin)
            {
                _logger?.LogWarning("[MARTINGALE] 🔄 [{Timestamp}] CORREÇÃO NECESSÁRIA! Último resultado: {LastResult}, Resultado correto: {CorrectedResult}",
                    timestamp, _lastResult, correctedIsWin);

                // Reverter o último processamento
                if (_lastResult) // Se pensávamos que era vitória mas na verdade foi perda
                {
                    _logger?.LogWarning("[MARTINGALE] 🔄 [{Timestamp}] Revertendo reset incorreto de vitória", timestamp);

                    // Restaurar estado de perda
                    _lossCount = 1; // Pelo menos 1 perda
                    _currentMartingaleLevel = 2; // Próximo nível
                    _needsMartingale = true;
                }
                else // Se pensávamos que era perda mas na verdade foi vitória
                {
                    _logger?.LogWarning("[MARTINGALE] 🔄 [{Timestamp}] Revertendo incremento incorreto de perda", timestamp);

                    // Restaurar estado de vitória
                    _lossCount = Math.Max(0, _lossCount - 1);
                    _currentMartingaleLevel = Math.Max(1, _currentMartingaleLevel - 1);

                    if (_lossCount == 0)
                    {
                        _currentMartingaleLevel = 1;
                        _needsMartingale = false;
                    }
                }

                // Atualizar último resultado
                _lastResult = correctedIsWin;

                _logger?.LogInformation("[MARTINGALE] 🔄 [{Timestamp}] Estado corrigido: Level={Level}, LossCount={LossCount}, NeedsMartingale={NeedsMartingale}",
                    timestamp, _currentMartingaleLevel, _lossCount, _needsMartingale);
            }
            else
            {
                _logger?.LogInformation("[MARTINGALE] ✅ [{Timestamp}] Resultado já estava correto, nenhuma correção necessária", timestamp);
            }
        }
    }
}

// EventArgs para notificar reset do martingale
public class MartingaleResetEventArgs : EventArgs
{
    public decimal OriginalStake { get; set; }
    public string ResetReason { get; set; } = string.Empty;
}
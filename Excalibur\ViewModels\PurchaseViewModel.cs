using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Linq;
using System.Timers;
using System.Collections.Generic;
using Excalibur.Models;
using Excalibur.Services;
using System.Diagnostics.CodeAnalysis;
using Microsoft.Extensions.Logging;

namespace Excalibur.ViewModels;

public class PurchaseViewModel : BaseViewModel, IDisposable
{
    public event EventHandler<ContractTransaction>? ContractExpired;
    public event EventHandler<MartingaleResetEventArgs>? MartingaleReset;
    private readonly ILogger<PurchaseViewModel> _logger;
    private ObservableCollection<ContractTransaction> _purchaseTransactions;
    private int _nextTransactionId = 1;
    private System.Timers.Timer _expirationTimer;
    private decimal _currentSpot;
    private readonly object _lock = new object(); // Objeto de sincronização
    private const int MAX_TRANSACTIONS = 50; // Limite para prevenir acúmulo excessivo
    private readonly MartingaleService _martingaleService;
    private MoneyManagementViewModel? _moneyManagementViewModel;
    private readonly HashSet<string> _processedContracts = new HashSet<string>(); // Para evitar processamento duplo
    private volatile bool _isProcessingMartingaleResult = false; // Flag para bloquear compras durante processamento

    public PurchaseViewModel(ILogger<PurchaseViewModel> logger, MoneyManagementViewModel? moneyManagementViewModel = null, MartingaleService? martingaleService = null)
    {
        _logger = logger;
        _purchaseTransactions = new ObservableCollection<ContractTransaction>();
        
        // Usar MartingaleService fornecido ou criar um novo
        _martingaleService = martingaleService ?? new MartingaleService();
        
        // Conectar evento de reset do martingale
        _martingaleService.MartingaleReset += OnMartingaleReset;
        
        _moneyManagementViewModel = moneyManagementViewModel;
        
        _logger.LogInformation("[PURCHASE] ⚡ Iniciando PurchaseViewModel...");
        
        // Timer para verificar expiração de contratos a cada segundo
        try
        {
            _expirationTimer = new System.Timers.Timer(1000);
            _expirationTimer.Elapsed += CheckContractExpirations;
            _expirationTimer.AutoReset = true;
            _expirationTimer.Start();
            
            _logger.LogInformation("[PURCHASE] ✅ Timer iniciado com sucesso! Intervalo: {Interval}ms", _expirationTimer.Interval);
            _logger.LogInformation("[PURCHASE] ✅ Timer.Enabled: {Enabled}", _expirationTimer.Enabled);
            _logger.LogInformation("[PURCHASE] ✅ Timer.AutoReset: {AutoReset}", _expirationTimer.AutoReset);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PURCHASE] ❌ Erro ao iniciar timer: {Message}", ex.Message);
        }
    }

    public ObservableCollection<ContractTransaction> PurchaseTransactions
    {
        get => _purchaseTransactions;
        set
        {
            _purchaseTransactions = value;
            OnPropertyChanged();
        }
    }

    // Propriedades calculadas para estatísticas
    public int TotalPurchaseEntries => _purchaseTransactions?.Count ?? 0;
    
    public decimal TotalProfitLoss 
    { 
        get 
        { 
            var total = _purchaseTransactions?.Sum(t => t.TotalProfitLoss) ?? 0;
            _logger.LogDebug("[PURCHASE] Calculando TotalProfitLoss: {Total:F2}", total);
            
            // Debug: listar todas as transações e seus valores
            if (_purchaseTransactions != null)
            {
                foreach (var t in _purchaseTransactions)
                {
                    _logger.LogDebug("[PURCHASE] Transação ID={Id}, RefId={RefId}, TotalProfitLoss={TotalProfitLoss:F2}", t.Id, t.RefId, t.TotalProfitLoss);
                }
            }
            
            return total;
        }
    }

    public void AddTransaction(ContractTransaction transaction)
    {
        // Garantir que a adição seja feita no thread correto
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == false)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() => AddTransaction(transaction));
            return;
        }

        lock (_lock)
        {
            // Atribuir ID se não tiver
            if (transaction.Id == 0)
            {
                transaction.Id = _nextTransactionId++;
            }
            
            _logger.LogInformation("[PURCHASE] 📝 Adicionando transação: ID={Id}, RefId={RefId}, Type={Type}, Stake={Stake}, Duration={Duration}{DurationType}", 
                transaction.Id, transaction.RefId, transaction.Type, transaction.Stake, transaction.Duration, transaction.DurationType);
            
            // Garantir que a transação seja marcada como ativa
            if (string.IsNullOrEmpty(transaction.RefId))
            {
                _logger.LogWarning("[PURCHASE] ⚠️ Transação sem RefId! Tipo: {Type}, Stake: {Stake}", transaction.Type, transaction.Stake);
            }
            else
            {
                // CORREÇÃO: Marcar como BUY para compras reais (não apenas verificar se é BUY)
                // Se o RefId contém apenas números, é uma compra real
                if (transaction.RefId.All(char.IsDigit))
                {
                    transaction.IsActive = true;
                    // CORREÇÃO: Não alterar o tipo - manter o nome original do contrato
                    _logger.LogInformation("[PURCHASE] 🔥 Transação marcada como COMPRA REAL ATIVA: RefId={RefId}, Type={Type}", transaction.RefId, transaction.Type);
                    
                    // CORREÇÃO: Não registrar contrato aqui para evitar registros duplos
                    // O registro será feito apenas em ExecuteNormalPurchase quando a compra for executada
                    _logger.LogInformation("[PURCHASE] 📝 Transação de compra real adicionada: RefId={RefId}, Type={Type} (registro no martingale será feito na execução)", transaction.RefId, transaction.Type);
                }
                else
                {
                    _logger.LogInformation("[PURCHASE] 📝 Transação de simulação adicionada: RefId={RefId}, Type={Type}", transaction.RefId, transaction.Type);
                }
            }
            
            // Garantir que os dados de expiração estejam corretos
            if (transaction.Duration <= 0)
            {
                transaction.Duration = 2; // Padrão: 2 ticks
                transaction.DurationType = "t";
                _logger.LogWarning("[PURCHASE] ⚠️ Duração não definida, usando padrão: 2t");
            }
            
            if (string.IsNullOrEmpty(transaction.DurationType))
            {
                transaction.DurationType = "t";
                _logger.LogWarning("[PURCHASE] ⚠️ Tipo de duração não definido, usando padrão: t");
            }
            
            var expirationTime = transaction.GetExpirationTimeInSeconds();
            _logger.LogInformation("[PURCHASE] 📝 Transação configurada: Duração={Duration}{DurationType}, Expiração={ExpirationTime}s", 
                transaction.Duration, transaction.DurationType, expirationTime);
            
            _purchaseTransactions.Add(transaction);
            
            // Limitar o número de transações para evitar acúmulo excessivo
            if (_purchaseTransactions.Count > MAX_TRANSACTIONS)
            {
                var oldestTransaction = _purchaseTransactions.First();
                _purchaseTransactions.Remove(oldestTransaction);
                _logger.LogInformation("[PURCHASE] 🧹 Transação mais antiga removida para manter limite de {MaxTransactions}", MAX_TRANSACTIONS);
            }
            
            _logger.LogInformation("[PURCHASE] 📝 Transação adicionada: ID={Id}, RefId={RefId}, BuyTime={BuyTime:HH:mm:ss}", 
                transaction.Id, transaction.RefId, transaction.BuyTime);
        }
        
        // Notificar mudanças nas propriedades
        OnPropertyChanged(nameof(TotalPurchaseEntries));
        OnPropertyChanged(nameof(TotalProfitLoss));
    }

    public void UpdateTransaction(string refId, decimal endSpot, decimal totalProfitLoss, bool isClosed = false)
    {
        var transaction = _purchaseTransactions.FirstOrDefault(t => t.RefId == refId);
        if (transaction != null)
        {
            transaction.EndSpot = endSpot;
            transaction.TotalProfitLoss = totalProfitLoss;
            
            if (isClosed)
            {
                transaction.SellTime = DateTime.Now;
                transaction.IsActive = false;
            }
            
            // Notificar mudança no lucro/prejuízo total
            OnPropertyChanged(nameof(TotalProfitLoss));
        }
    }
    
    public void UpdateContractId(string oldProposalId, string newContractId)
    {
        lock (_lock)
        {
            var transaction = _purchaseTransactions.FirstOrDefault(t => t.RefId == oldProposalId);
            if (transaction != null)
            {
                transaction.RefId = newContractId;
                _logger.LogInformation("[PURCHASE] RefId atualizado: {Old} -> {New}", oldProposalId, newContractId);
                
                // IMPORTANTE: Informar ao MartingaleService sobre o novo Contract ID
                // Se o contrato ativo no martingale era a proposta antiga, atualizar para o novo contract ID
                if (_moneyManagementViewModel?.MartingaleEnabled == true)
                {
                    var currentActiveId = _martingaleService.GetActiveContractId();
                    if (currentActiveId == oldProposalId)
                    {
                        // Atualizar o ID do contrato ativo no martingale para o novo contract ID
                        _martingaleService.UpdateActiveContractId(newContractId);
                        _logger.LogInformation("[PURCHASE] MartingaleService: Active Contract ID atualizado {Old} -> {New}", oldProposalId, newContractId);
                        
                        // LOG INFORMATIVO para aparecer no arquivo log.txt
                        _logger.LogInformation("[PURCHASE] MartingaleService: Active Contract ID atualizado {Old} -> {New}", oldProposalId, newContractId);
                    }
                }
            }
        }
    }

    public void UpdateCurrentSpot(decimal currentSpot)
    {
        _currentSpot = currentSpot;
        _logger.LogInformation("UPDATE_SPOT: Novo spot={Spot}, Contratos ativos={ActiveContracts}", currentSpot, _purchaseTransactions.Count(t => t.IsActive));
        
        lock(_lock) // Bloquear acesso concorrente à coleção
        {
            // Atualizar apenas contratos ativos
            foreach (var transaction in _purchaseTransactions.Where(t => t.IsActive))
            {
                // Atualizar EndSpot a cada tick para refletir a cotação mais recente
                transaction.EndSpot = currentSpot;

                // Se ainda não temos StartSpot válido, capturar agora (primeiro tick)
                if (transaction.StartSpot == 0)
                {
                    transaction.StartSpot = currentSpot;
                }
                
                // CORREÇÃO: Durante a execução do contrato, mostrar P/L em tempo real baseado em win/loss para contratos binários
                if (!transaction.ProfitFromApi && transaction.IsActive)
                {
                    // Para contratos binários, verificar se está ganhando e usar valores fixos
                    bool isCurrentlyWinning = transaction.IsCurrentlyWinning(currentSpot);

                    if (isCurrentlyWinning)
                    {
                        // CORREÇÃO: Se está ganhando: Payout - Stake (lucro líquido)
                        transaction.TotalProfitLoss = transaction.Payout - transaction.Stake;
                    }
                    else
                    {
                        // Se está perdendo: -Stake (perda total)
                        transaction.TotalProfitLoss = -transaction.Stake;
                    }
                }
            }
        }
        
        // Sempre notificar mudança no lucro/prejuízo total 
        OnPropertyChanged(nameof(TotalProfitLoss));
    }

    private void CheckContractExpirations(object sender, ElapsedEventArgs e)
    {
        var now = DateTime.Now;
        
        // Só log detalhado se há contratos ativos
        var activeContractsCount = _purchaseTransactions.Count(t => t.IsActive);
        if (activeContractsCount > 0)
        {
            _logger.LogInformation("[PURCHASE] ⏰ Timer executado - {Active} contratos ativos", activeContractsCount);
        }
        
        List<ContractTransaction> expiredContracts = new List<ContractTransaction>();
        
        lock(_lock) // Bloquear acesso concorrente à coleção
        {
            // Verificar contratos ativos
            var allActiveContracts = _purchaseTransactions.Where(t => t.IsActive && !string.IsNullOrEmpty(t.RefId)).ToList();
            
            // Log só se há contratos para verificar
            if (allActiveContracts.Count > 0)
            {
                _logger.LogInformation("[PURCHASE] ⏰ Verificando {Count} contratos ativos", allActiveContracts.Count);
            }
            
            foreach (var contract in allActiveContracts)
            {
                // Verificar se já foi processado
                if (_processedContracts.Contains(contract.RefId))
                {
                    continue;
                }
                
                // Verificar se expirou baseado no BuyTime
                var timeElapsed = DateTime.Now - contract.BuyTime;
                var expirationTimeInSeconds = contract.GetExpirationTimeInSeconds();
                bool isExpired = timeElapsed.TotalSeconds >= expirationTimeInSeconds;
                
                if (isExpired)
                {
                    _logger.LogInformation("[PURCHASE] ⏰ ✅ Contrato {RefId} EXPIRADO! Tempo decorrido: {TimeElapsed}s", 
                        contract.RefId, timeElapsed.TotalSeconds);
                    expiredContracts.Add(contract);
                }
                // Remover log verboso do tempo restante para reduzir processamento
            }
        }
        
        // Log só se encontrou contratos expirados
        if (expiredContracts.Count > 0)
        {
            _logger.LogInformation("[PURCHASE] ⏰ Encontrados {Count} contratos expirados", expiredContracts.Count);
        }
        
        // Processar expirações fora do lock
        foreach (var expiredContract in expiredContracts)
        {
            _logger.LogInformation("[PURCHASE] ⏰ Processando expiração: RefId={RefId}, Type={Type}", expiredContract.RefId, expiredContract.Type);
            
            // Marcar como processado
            _processedContracts.Add(expiredContract.RefId);
            
            // Atualizar contratos no UI thread
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                expiredContract.IsActive = false;
                expiredContract.EndSpot = _currentSpot;
                expiredContract.SellTime = DateTime.Now;
                
                // Calcular resultado baseado no tipo de contrato
                bool isWin = expiredContract.IsCurrentlyWinning(_currentSpot);
                
                if (isWin)
                {
                    // CORREÇÃO: Para contratos que ganham, P/L = Payout - Stake
                    expiredContract.TotalProfitLoss = expiredContract.Payout - expiredContract.Stake;
                    _logger.LogInformation("[PURCHASE] ✅ Contrato {RefId} GANHOU! P/L={ProfitLoss} (Payout: {Payout} - Stake: {Stake})",
                        expiredContract.RefId, expiredContract.TotalProfitLoss, expiredContract.Payout, expiredContract.Stake);
                }
                else
                {
                    // Para contratos que perdem, P/L = -Stake
                    expiredContract.TotalProfitLoss = -expiredContract.Stake;
                    _logger.LogInformation("[PURCHASE] ❌ Contrato {RefId} PERDEU! P/L={ProfitLoss} (Perda: -{Stake})",
                        expiredContract.RefId, expiredContract.TotalProfitLoss, expiredContract.Stake);
                }
                
                // Notificar mudanças na UI
                OnPropertyChanged(nameof(TotalProfitLoss));
                OnPropertyChanged(nameof(TotalPurchaseEntries));
            });
            
            // CORREÇÃO: Aguardar dados da API antes de processar no Martingale para evitar resets incorretos
            if (_moneyManagementViewModel != null && _moneyManagementViewModel.MartingaleEnabled)
            {
                _logger.LogInformation("[PURCHASE] 🎯 Agendando processamento do resultado no sistema martingale após dados da API...");

                // Agendar processamento após dados da API
                _ = Task.Run(async () =>
                {
                    await Task.Delay(3000); // Aguardar 3 segundos para dados da API

                    bool finalResult;
                    bool hasApiData = false;

                    lock (_lock)
                    {
                        var contract = _purchaseTransactions.FirstOrDefault(t => t.RefId == expiredContract.RefId);
                        if (contract != null && contract.ProfitFromApi)
                        {
                            finalResult = contract.TotalProfitLoss > 0;
                            hasApiData = true;
                            _logger.LogInformation("[PURCHASE] 🔄 API forneceu resultado: RefId={RefId}, P/L={ProfitLoss}, IsWin={IsWin}",
                                contract.RefId, contract.TotalProfitLoss, finalResult);
                        }
                        else
                        {
                            // CORREÇÃO: Se API não forneceu dados, assumir PERDA para preservar Martingale
                            finalResult = false;
                            _logger.LogWarning("[PURCHASE] ⚠️ API não forneceu dados para RefId={RefId}, assumindo PERDA para preservar Martingale",
                                expiredContract.RefId);
                        }
                    }

                    // Processar resultado no sistema martingale
                    _logger.LogInformation("[PURCHASE] 🎯 Processando resultado no sistema martingale: RefId={RefId}, IsWin={IsWin}, HasApiData={HasApiData}",
                        expiredContract.RefId, finalResult, hasApiData);

                    ProcessMartingaleResult(finalResult, expiredContract.RefId);
                });
            }
            
            // Disparar evento de expiração
            ContractExpired?.Invoke(this, expiredContract);
        }
        
        // Se houve contratos expirados, notificar mudança no total
        if (expiredContracts.Count > 0)
        {
            OnPropertyChanged(nameof(TotalProfitLoss));
            
            // Limpar lista de contratos processados periodicamente (manter só os últimos 100)
            lock (_lock)
            {
                if (_processedContracts.Count > 100)
                {
                    var toRemove = _processedContracts.Take(_processedContracts.Count - 50).ToList();
                    foreach (var id in toRemove)
                    {
                        _processedContracts.Remove(id);
                    }
                    _logger.LogInformation("[PURCHASE] 🧹 Lista de contratos processados limpa. Count: {Count}", _processedContracts.Count);
                }
            }
        }
        
        // Remover log verboso do fim da execução para reduzir processamento
    }

    public void UpdateProfitFromApi(string refId, decimal profit)
    {
        _logger.LogInformation("ATUALIZANDO: Tentando atualizar P/L para RefId={RefId}, Profit={Profit}", refId, profit);
        
        lock(_lock) // Bloquear acesso concorrente à coleção
        {
            var transaction = _purchaseTransactions.FirstOrDefault(t => t.RefId == refId);
            if (transaction != null)
            {
                // CORREÇÃO: Sempre atualizar o P/L da API, mesmo se inativo
                // Isso garante que contratos expirados mostrem o resultado correto
                decimal previousProfit = transaction.TotalProfitLoss;
                bool wasWin = previousProfit > 0;
                bool isWin = profit > 0;

                _logger.LogInformation("ENCONTRADO: RefId={RefId}, IsActive={IsActive}, P/L anterior={PreviousProfit}, novo P/L={NewProfit}",
                    refId, transaction.IsActive, previousProfit, profit);

                // Detectar se houve mudança no resultado (vitória/derrota)
                if (!transaction.IsActive && transaction.ProfitFromApi && wasWin != isWin)
                {
                    _logger.LogWarning("🔄 CORREÇÃO DE RESULTADO DETECTADA: RefId={RefId}, Era Win={WasWin}, Agora Win={IsWin}",
                        refId, wasWin, isWin);

                    // CORREÇÃO: Corrigir resultado no sistema Martingale
                    if (_moneyManagementViewModel != null && _moneyManagementViewModel.MartingaleEnabled)
                    {
                        _logger.LogInformation("🔄 Corrigindo resultado no sistema Martingale...");
                        _martingaleService.CorrectContractResult(refId, isWin);
                    }
                }

                transaction.TotalProfitLoss = profit;
                transaction.ProfitFromApi = true;

                // Forçar atualização da UI
                transaction.OnPropertyChanged(nameof(transaction.TotalProfitLoss));
                transaction.OnPropertyChanged(nameof(transaction.TotalProfitLossString));

                // Notificar mudança no total geral
                OnPropertyChanged(nameof(TotalProfitLoss));
            }
            else
            {
                _logger.LogInformation("NÃO ENCONTRADO: RefId={RefId}, Profit={Profit}. Transações disponíveis:", refId, profit);
                foreach (var t in _purchaseTransactions.Take(5)) // Mostrar apenas as 5 mais recentes
                {
                    _logger.LogInformation("  - RefId={RefId}, IsActive={IsActive}, P/L={ProfitLoss}", t.RefId, t.IsActive, t.TotalProfitLoss);
                }
            }
        }
    }
    
    // Método para atualizar por proximidade de hora, quando IDs não são confiáveis
    public void UpdateProfitFromApiByTime(DateTime apiDateTime, decimal profit, int secondsWindow = 30)
    {
        _logger.LogInformation("TENTANDO MATCH POR HORA: API DateTime={ApiDateTime}, Profit={Profit}", apiDateTime, profit);
        
        lock(_lock)
        {
            // Verificar transações que têm horário de compra próximo ao horário da API
            var startWindow = apiDateTime.AddSeconds(-secondsWindow);
            var endWindow = apiDateTime.AddSeconds(secondsWindow);
            
            var matchingTransaction = _purchaseTransactions
                .Where(t => t.BuyTime.ToUniversalTime() >= startWindow && 
                           t.BuyTime.ToUniversalTime() <= endWindow)
                .OrderBy(t => Math.Abs((t.BuyTime.ToUniversalTime() - apiDateTime).TotalSeconds))
                .FirstOrDefault();
                
            if (matchingTransaction != null)
            {
                // CORREÇÃO: Só atualizar se o contrato ainda estiver ativo
                // Se já expirou, não sobrescrever o valor calculado localmente
                if (matchingTransaction.IsActive)
                {
                    _logger.LogInformation("MATCH POR HORA ENCONTRADO E ATIVO: RefId={RefId}, " +
                        "BuyTime={BuyTime}, API DateTime={ApiDateTime}, " +
                        "P/L anterior={PreviousProfit}, novo P/L={NewProfit}", matchingTransaction.RefId, matchingTransaction.BuyTime, apiDateTime, matchingTransaction.TotalProfitLoss, profit);
                    
                    matchingTransaction.TotalProfitLoss = profit;
                    matchingTransaction.ProfitFromApi = true;
                    // Forçar atualização da UI
                    matchingTransaction.OnPropertyChanged(nameof(matchingTransaction.TotalProfitLoss));
                    matchingTransaction.OnPropertyChanged(nameof(matchingTransaction.TotalProfitLossString));
                    
                    // Notificar mudança no total geral
                    OnPropertyChanged(nameof(TotalProfitLoss));
                }
                else
                {
                    _logger.LogInformation("CONTRATO JÁ EXPIRADO POR HORA: RefId={RefId}, mantendo P/L calculado localmente: {LocalProfit}, ignorando P/L da API: {ApiProfit}", 
                        matchingTransaction.RefId, matchingTransaction.TotalProfitLoss, profit);
                }
            }
            else
            {
                _logger.LogInformation("NENHUM MATCH POR HORA: API DateTime={ApiDateTime}, janela={StartWindow} a {EndWindow}", apiDateTime, startWindow, endWindow);
            }
        }
    }

    public void ClearTransactions()
    {
        // Garantir que a limpeza seja feita no thread correto
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == false)
        {
            System.Windows.Application.Current.Dispatcher.Invoke(() => ClearTransactions());
            return;
        }

        lock (_lock)
        {
            _logger.LogInformation("[PURCHASE] Limpando {Count} transações de compra", _purchaseTransactions.Count);
            _purchaseTransactions.Clear();
            _processedContracts.Clear(); // Limpar contratos processados
            _nextTransactionId = 1; // Reset do contador de IDs
            _logger.LogInformation("[PURCHASE] Tabela de compras limpa");
        }
        
        // Reset do sistema de martingale
        _martingaleService.ResetMartingale();
        _logger.LogInformation("[PURCHASE] Sistema de martingale resetado");
        
        // Notificar mudança na propriedade
        OnPropertyChanged(nameof(PurchaseTransactions));
        OnPropertyChanged(nameof(TotalPurchaseEntries));
        OnPropertyChanged(nameof(TotalProfitLoss));
    }

    public async System.Threading.Tasks.Task<bool> TryExecuteMartingaleEntryAsync(decimal baseStake, ProposalViewModel proposalViewModel)
    {
        _logger.LogInformation("[PURCHASE] 📥 TryExecuteMartingaleEntryAsync chamado! BaseStake: {BaseStake}", baseStake);
        
        if (_moneyManagementViewModel == null || !_moneyManagementViewModel.MartingaleEnabled)
        {
            _logger.LogInformation("[PURCHASE] ❌ Martingale desabilitado ou MoneyManagement null!");
            return false;
        }

        // NOVA LÓGICA: Verificar se pode aceitar entrada
        if (!_martingaleService.CanAcceptNewEntry())
        {
            _logger.LogInformation("[PURCHASE] ❌ ENTRADA REJEITADA! Sistema martingale tem contrato ativo.");
            LogMartingaleSystemState();
            return false;
        }

        _logger.LogInformation("[PURCHASE] ✅ Martingale habilitado e pode aceitar entrada!");
        
        // Log do estado atual do sistema
        LogMartingaleSystemState();
        
        // CORREÇÃO: Verificar se o sistema foi inicializado corretamente
        // Se _baseMartingaleStake é 0, significa que nunca foi inicializado com as configurações do usuário
        var currentBaseStake = _martingaleService.GetBaseMartingaleStake();
        var currentFactor = _martingaleService.GetMartingaleFactor();
        var expectedFactor = _moneyManagementViewModel?.Factor ?? 2m;

        _logger.LogInformation("[PURCHASE] 🔍 Verificando inicialização do sistema:");
        _logger.LogInformation("[PURCHASE] 🔍   - Current Base Stake: {CurrentBaseStake}", currentBaseStake);
        _logger.LogInformation("[PURCHASE] 🔍   - Current Factor: {CurrentFactor}", currentFactor);
        _logger.LogInformation("[PURCHASE] 🔍   - Expected Factor: {ExpectedFactor}", expectedFactor);

        // Inicializar se nunca foi inicializado OU se o fator está incorreto
        if (currentBaseStake == 0 || Math.Abs(currentFactor - expectedFactor) > 0.01m)
        {
            _logger.LogInformation("[PURCHASE] 🔧 Inicializando/Corrigindo sistema martingale com baseStake: {BaseStake}, Factor: {Factor}", baseStake, expectedFactor);
            InitializeMartingaleSystem(baseStake);
        }
        else
        {
            _logger.LogInformation("[PURCHASE] ⚡ Sistema martingale já inicializado corretamente, usando configuração existente");
        }

        // Criar função para executar compra real via API que retorna Contract ID
        Func<decimal, System.Threading.Tasks.Task<string>> executePurchase = async (stake) =>
        {
            _logger.LogInformation("[MARTINGALE-EXEC] 🔥🔥🔥 FUNÇÃO executePurchase CHAMADA! 🔥🔥🔥");
            _logger.LogInformation("[MARTINGALE-EXEC] 🔥 Stake recebido: {Stake}", stake);
            _logger.LogInformation("[MARTINGALE-EXEC] 🔥 ProposalViewModel é null? {IsNull}", proposalViewModel == null);
            
            try
            {
                _logger.LogInformation("[MARTINGALE-EXEC] ✅ Executando compra com stake: {Stake}", stake);
                
                string contractId = string.Empty;
                
                // Usar Dispatcher para executar no thread da UI
                var dispatcher = System.Windows.Application.Current?.Dispatcher;
                if (dispatcher != null)
                {
                    await dispatcher.InvokeAsync(async () =>
                    {
                        try
                        {
                            // Log do estado antes da atualização
                            _logger.LogInformation("[MARTINGALE-EXEC] 🔧 Estado antes da atualização:");
                            _logger.LogInformation("[MARTINGALE-EXEC] 🔧   - Stake recebido: {ReceivedStake}", stake);
                            _logger.LogInformation("[MARTINGALE-EXEC] 🔧   - Stake atual do Proposal: {CurrentStake}", proposalViewModel.Stake);

                            // Atualizar stake no proposalViewModel
                            _logger.LogInformation("[MARTINGALE-EXEC] 🔧 Atualizando stake para: {Stake}", stake);
                            proposalViewModel.SetStake(stake);

                            // Verificar se a atualização funcionou
                            _logger.LogInformation("[MARTINGALE-EXEC] 🔧 Estado após SetStake:");
                            _logger.LogInformation("[MARTINGALE-EXEC] 🔧   - Stake do Proposal: {ProposalStake}", proposalViewModel.Stake);

                        // Delay aumentado para garantir que a proposta seja calculada
                        _logger.LogInformation("[MARTINGALE-EXEC] ⏳ Aguardando atualização da proposta...");
                        await System.Threading.Tasks.Task.Delay(500);

                        // Verificar estado final antes da compra
                        _logger.LogInformation("[MARTINGALE-EXEC] 🔧 Estado final antes da compra:");
                        _logger.LogInformation("[MARTINGALE-EXEC] 🔧   - Stake do Proposal: {FinalStake}", proposalViewModel.Stake);
                        _logger.LogInformation("[MARTINGALE-EXEC] 🔧   - Payout do Proposal: {FinalPayout}", proposalViewModel.Payout);

                        // Executar compra real via API usando o método original
                        _logger.LogInformation("[MARTINGALE-EXEC] 🚀 Iniciando ExecuteRealPurchaseAsync com stake: {Stake}...", stake);
                        contractId = await proposalViewModel.ExecuteRealPurchaseAsync(stake);

                        _logger.LogInformation("[MARTINGALE-EXEC] ✅ Contract ID retornado: {ContractId}", contractId);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "[MARTINGALE] Erro ao executar compra real: {Message}", ex.Message);
                        }
                    });
                }
                
                return contractId;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[MARTINGALE] Erro geral ao executar compra: {Message}", ex.Message);
                return string.Empty;
            }
        };

        // Enfileirar entrada com groupId baseado no tipo de contrato
        string contractType = proposalViewModel.SelectedContract?.ContractTypeValue ?? "Unknown";
        string groupId = $"{contractType}_{DateTime.Now:yyyyMMdd}";
        
        _logger.LogInformation("[PURCHASE] 🔄 Tentando executar entrada no MartingaleService...");
        _logger.LogInformation("[PURCHASE] 🔄 GroupId: {GroupId}", groupId);
        _logger.LogInformation("[PURCHASE] 🔄 BaseStake passado: {BaseStake}", baseStake);
        _logger.LogInformation("[PURCHASE] 🔄 Nível martingale atual: {MartingaleLevel}", _martingaleService.GetCurrentMartingaleLevel());
        
        // Calcular stake esperado para comparação
        var expectedStake = _martingaleService.GetCurrentMartingaleLevel() > 1 ? 
            baseStake * (decimal)Math.Pow((double)_moneyManagementViewModel.Factor, _martingaleService.GetCurrentMartingaleLevel() - 1) : 
            baseStake;
        _logger.LogInformation("[PURCHASE] 🔄 Stake esperado com martingale: {ExpectedStake}", expectedStake);
        
        bool success = await _martingaleService.TryExecuteEntryAsync(baseStake, executePurchase, groupId);
        
        if (success)
        {
            _logger.LogInformation("[PURCHASE] ✅ Entrada executada com sucesso no MartingaleService!");
        }
        else
        {
            _logger.LogInformation("[PURCHASE] ❌ Falha ao executar entrada no MartingaleService!");
        }
        
        return success;
    }

    public void InitializeMartingaleSystem(decimal baseStake)
    {
        if (_moneyManagementViewModel == null || !_moneyManagementViewModel.MartingaleEnabled)
        {
            _logger.LogInformation("[PURCHASE] ❌ Martingale desabilitado ou MoneyManagement null");
            return;
        }

        _logger.LogInformation("[PURCHASE] ⚙️ Inicializando sistema martingale com:");
        _logger.LogInformation("[PURCHASE] ⚙️ Base Stake: {BaseStake}", baseStake);
        _logger.LogInformation("[PURCHASE] ⚙️ Factor: {Factor}", _moneyManagementViewModel.Factor);
        _logger.LogInformation("[PURCHASE] ⚙️ Level: {Level}", _moneyManagementViewModel.Level);

        // NOVA LÓGICA: Usar InitializeMartingaleSettings
        _martingaleService.InitializeMartingaleSettings(
            baseStake,
            _moneyManagementViewModel.Factor,
            _moneyManagementViewModel.Level
        );
        
        // Manter compatibilidade com sistema legado
        _martingaleService.GetOrCreateGlobalMartingaleGroup(
            baseStake,
            _moneyManagementViewModel.Factor,
            _moneyManagementViewModel.Level
        );
        
        _logger.LogInformation("[PURCHASE] ✅ Sistema martingale inicializado com sucesso");
    }

    public void ProcessMartingaleResult(bool isWin, string contractId = "")
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
        var stackTrace = new System.Diagnostics.StackTrace();
        var callingMethod = stackTrace.GetFrame(1)?.GetMethod()?.Name ?? "Unknown";

        _logger.LogInformation("[PURCHASE] 🎯 [{Timestamp}] ProcessMartingaleResult chamado! IsWin: {IsWin}, ContractId: {ContractId}", timestamp, isWin, contractId);
        _logger.LogInformation("[PURCHASE] 🎯 [{Timestamp}] Chamado por: {CallingMethod}", timestamp, callingMethod);
        _logger.LogInformation("[PURCHASE] 🎯 [{Timestamp}] Thread: {ThreadId}", timestamp, System.Threading.Thread.CurrentThread.ManagedThreadId);

        // CORREÇÃO: Definir flag para bloquear novas compras durante processamento
        _isProcessingMartingaleResult = true;
        _logger.LogInformation("[PURCHASE] 🔒 [{Timestamp}] Bloqueando novas compras durante processamento martingale", timestamp);

        try
        {
            _martingaleService.ProcessContractExpiration(contractId, isWin);
            _logger.LogInformation("[PURCHASE] 🎯 [{Timestamp}] Resultado processado pelo MartingaleService", timestamp);
        }
        finally
        {
            // CORREÇÃO: Sempre liberar flag, mesmo em caso de erro
            _isProcessingMartingaleResult = false;
            _logger.LogInformation("[PURCHASE] 🔓 [{Timestamp}] Liberando bloqueio de compras após processamento martingale", timestamp);
        }
    }

    public void SetMoneyManagementViewModel(MoneyManagementViewModel moneyManagementViewModel)
    {
        _moneyManagementViewModel = moneyManagementViewModel;
    }

    // Nova função para configurar verificador de critérios de simulação
    public void ConfigureSimulationCriteriaChecker(Func<System.Threading.Tasks.Task<bool>> checker)
    {
        _martingaleService.SetSimulationCriteriaChecker(checker);
        _logger.LogInformation("[PURCHASE] 🔍 Verificador de critérios de simulação configurado no MartingaleService");
    }

    private void LogMartingaleSystemState()
    {
        _logger.LogInformation("[PURCHASE] 📊 Solicitando estado detalhado do sistema martingale...");
        _martingaleService.LogSystemState("PurchaseViewModel");
    }

    public int GetActiveContractsCount()
    {
        lock (_lock)
        {
            var count = _purchaseTransactions.Count(t => t.IsActive);
            _logger.LogInformation("[PURCHASE] 📊 Contratos ativos: {Count}", count);
            return count;
        }
    }

    private void OnMartingaleReset(object? sender, MartingaleResetEventArgs e)
    {
        _logger.LogInformation("[PURCHASE] 🔔 Martingale resetado! Motivo: {Reason}, Stake original: {OriginalStake}", 
            e.ResetReason, e.OriginalStake);
            
        // Repassar evento para interessados (como ProposalViewModel)
        MartingaleReset?.Invoke(this, e);
    }

    public bool ShouldApplyMartingale()
    {
        if (_moneyManagementViewModel == null || !_moneyManagementViewModel.MartingaleEnabled)
        {
            _logger.LogInformation("[PURCHASE] 🚫 Martingale desabilitado");
            return false;
        }

        // CORREÇÃO: Verificar se está processando resultado de martingale
        if (_isProcessingMartingaleResult)
        {
            _logger.LogInformation("[PURCHASE] ⏳ Aguardando processamento de resultado martingale - bloqueando nova compra");
            return false;
        }

        // Verificar se há contrato ativo (não pode aplicar durante contrato ativo)
        if (_martingaleService.HasActiveContract())
        {
            _logger.LogInformation("[PURCHASE] ⏳ Contrato ativo - não aplicar martingale agora");
            return false;
        }

        // CORREÇÃO: Verificar se o sistema foi inicializado
        var currentBaseStake = _martingaleService.GetBaseMartingaleStake();
        if (currentBaseStake == 0)
        {
            _logger.LogInformation("[PURCHASE] ⚠️ MartingaleService não inicializado (BaseStake=0) - não aplicar martingale");
            return false;
        }

        // CORREÇÃO: Aplicar martingale se:
        // 1. Nível atual > 1 (indica que houve perdas anteriores)
        // 2. OU se explicitamente há necessidade de martingale
        // 3. OU se há perdas consecutivas registradas
        // 4. OU se há perdas recentes não processadas
        int currentLevel = _martingaleService.GetCurrentMartingaleLevel();
        bool needsMartingale = _martingaleService.NeedsMartingale();
        int lossCount = _martingaleService.GetCurrentLossCount();

        // CORREÇÃO: Considerar também o histórico de perdas recentes
        bool hasRecentLosses = HasRecentConsecutiveLosses();

        // CORREÇÃO: Verificar se há perdas não processadas pelo Martingale
        bool hasUnprocessedLosses = HasUnprocessedLosses();

        bool shouldApply = currentLevel > 1 || needsMartingale || lossCount > 0 || hasRecentLosses || hasUnprocessedLosses;

        _logger.LogInformation("[PURCHASE] 🔍 ShouldApplyMartingale: {ShouldApply} (Level: {Level}, NeedsMartingale: {NeedsMartingale}, LossCount: {LossCount}, RecentLosses: {RecentLosses}, UnprocessedLosses: {UnprocessedLosses})",
            shouldApply, currentLevel, needsMartingale, lossCount, hasRecentLosses, hasUnprocessedLosses);

        return shouldApply;
    }

    // CORREÇÃO: Método para verificar se há perdas não processadas pelo Martingale
    private bool HasUnprocessedLosses()
    {
        try
        {
            // Verificar se há contratos com P/L negativo que não foram processados pelo Martingale
            var unprocessedLosses = _purchaseTransactions
                .Where(t => !t.IsActive && t.ProfitFromApi && t.TotalProfitLoss < 0)
                .OrderByDescending(t => t.BuyTime)
                .Take(5) // Verificar as últimas 5 transações
                .ToList();

            if (unprocessedLosses.Count == 0)
            {
                return false;
            }

            // Verificar se a última perda foi processada pelo Martingale
            var lastLoss = unprocessedLosses.First();
            bool wasProcessed = _martingaleService.WasContractProcessed(lastLoss.RefId);

            _logger.LogInformation("[PURCHASE] 🔍 Verificando perdas não processadas: Última perda RefId={RefId}, P/L={ProfitLoss}, Processada={Processed}",
                lastLoss.RefId, lastLoss.TotalProfitLoss, wasProcessed);

            return !wasProcessed;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PURCHASE] ❌ Erro ao verificar perdas não processadas");
            return false;
        }
    }

    // CORREÇÃO: Método para verificar se há perdas consecutivas recentes
    private bool HasRecentConsecutiveLosses()
    {
        try
        {
            // Verificar as últimas 3 transações para identificar perdas consecutivas
            var recentTransactions = _purchaseTransactions
                .Where(t => !t.IsActive) // Apenas contratos expirados
                .OrderByDescending(t => t.BuyTime)
                .Take(3)
                .ToList();

            if (recentTransactions.Count == 0)
            {
                return false;
            }

            // Verificar se a última transação foi uma perda
            var lastTransaction = recentTransactions.First();
            bool lastWasLoss = lastTransaction.TotalProfitLoss < 0;

            _logger.LogInformation("[PURCHASE] 🔍 Verificando perdas recentes: Última transação P/L={ProfitLoss}, Foi perda={WasLoss}",
                lastTransaction.TotalProfitLoss, lastWasLoss);

            return lastWasLoss;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PURCHASE] ❌ Erro ao verificar perdas recentes");
            return false;
        }
    }

    public async System.Threading.Tasks.Task<bool> ExecuteNormalPurchaseAsync(decimal baseStake, ProposalViewModel proposalViewModel)
    {
        _logger.LogInformation("[PURCHASE] 💰 ExecuteNormalPurchaseAsync - Compra normal sem martingale");
        _logger.LogInformation("[PURCHASE] 💰 BaseStake: {BaseStake}", baseStake);

        // CORREÇÃO: Garantir que o MartingaleService seja inicializado mesmo para compras normais
        if (_moneyManagementViewModel != null && _moneyManagementViewModel.MartingaleEnabled)
        {
            var currentBaseStake = _martingaleService.GetBaseMartingaleStake();
            if (currentBaseStake == 0)
            {
                _logger.LogInformation("[PURCHASE] 🔧 Inicializando MartingaleService para compra normal com baseStake: {BaseStake}", baseStake);
                InitializeMartingaleSystem(baseStake);
            }
            else
            {
                _logger.LogInformation("[PURCHASE] ✅ MartingaleService já inicializado com BaseStake: {CurrentBaseStake}", currentBaseStake);
            }
        }

        try
        {
            // Atualizar stake no proposalViewModel para o valor normal
            proposalViewModel.SetStake(baseStake);
            
            // Aguardar atualização da proposta
            await System.Threading.Tasks.Task.Delay(500);
            
            // Executar compra real via API
            string contractId = await proposalViewModel.ExecuteRealPurchaseAsync();
            
            if (!string.IsNullOrEmpty(contractId))
            {
                _logger.LogInformation("[PURCHASE] ✅ Compra normal executada com sucesso! Contract ID: {ContractId}", contractId);
                
                // Registrar o contrato no sistema martingale para controle
                _martingaleService.RegisterActiveContract(contractId);
                
                return true;
            }
            else
            {
                _logger.LogInformation("[PURCHASE] ❌ Falha na compra normal - Contract ID vazio");
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PURCHASE] ❌ Erro ao executar compra normal: {Message}", ex.Message);
            return false;
        }
    }

    public void Dispose()
    {
        try
        {
            _logger.LogInformation("[PURCHASE] 🛑 Disposing PurchaseViewModel...");
            
            _expirationTimer?.Stop();
            _expirationTimer?.Dispose();
            
            // Desconectar evento do MartingaleService
            if (_martingaleService != null)
            {
                _martingaleService.MartingaleReset -= OnMartingaleReset;
            }
            
            _logger.LogInformation("[PURCHASE] ✅ Timer parado e liberado");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PURCHASE] ❌ Erro ao fazer dispose: {Message}", ex.Message);
        }
    }
} 
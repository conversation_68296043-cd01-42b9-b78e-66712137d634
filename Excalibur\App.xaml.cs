﻿using System.IO;
using System.Windows;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Excalibur.Core.Interfaces;
using Excalibur.Core.Services;
using Excalibur.ViewModels;
using Excalibur.Services;
using System.Windows.Threading;
using System.Threading.Tasks;
using System;
using Excalibur.Tests;

namespace Excalibur;

public partial class App : Application
{
    private IHost? _host;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // Verificar se é para executar testes
        if (e.Args.Length > 0 && e.Args[0] == "test")
        {
            await RunMartingaleTests();
            Shutdown();
            return;
        }

        _host = Host.CreateDefaultBuilder()
            .ConfigureServices((context, services) =>
            {
                // Registrar serviços
                services.AddSingleton<IDerivApiService, DerivApiService>();

                // Registrar ViewModels
                services.AddTransient<MainViewModel>();
                services.AddSingleton<AccountInfoViewModel>();
                services.AddSingleton<LogViewModel>();
                services.AddSingleton<ActiveSymbolsViewModel>();
                services.AddSingleton<ProposalViewModel>();

                // Logging provider
                services.AddLogging(builder =>
                {
                    builder.AddDebug();
                    builder.SetMinimumLevel(LogLevel.Debug);
                });

                // Registrar Views
                services.AddTransient<MainWindow>();
            })
            .Build();

        base.OnStartup(e);

        var mainWindow = _host.Services.GetRequiredService<MainWindow>();
        
        // Obter os loggers necessários
        var purchaseLogger = _host.Services.GetRequiredService<ILogger<PurchaseViewModel>>();
        var martingaleLogger = _host.Services.GetRequiredService<ILogger<MartingaleService>>();
        
        // Criar MainViewModel manualmente com os loggers
        var derivApiService = _host.Services.GetRequiredService<IDerivApiService>();
        var accountInfoViewModel = _host.Services.GetRequiredService<AccountInfoViewModel>();
        var activeSymbolsViewModel = _host.Services.GetRequiredService<ActiveSymbolsViewModel>();
        var proposalViewModel = _host.Services.GetRequiredService<ProposalViewModel>();
        
        var mainViewModel = new MainViewModel(derivApiService, accountInfoViewModel, activeSymbolsViewModel, proposalViewModel, purchaseLogger, martingaleLogger);

        mainWindow.DataContext = mainViewModel;
        mainWindow.Show();

        // Reset log file on startup
        var logFilePath = "log.txt";
        if (File.Exists(logFilePath))
        {
            File.Delete(logFilePath);
        }

        // Add file logger
        var loggerFactory = _host.Services.GetRequiredService<ILoggerFactory>();
        loggerFactory.AddProvider(new Excalibur.Infrastructure.FileLoggerProvider(logFilePath));
    }

    protected override void OnExit(ExitEventArgs e)
    {
        _host?.Dispose();
        base.OnExit(e);
    }

    public App()
    {
        DispatcherUnhandledException += (s, args) =>
        {
            MessageBox.Show(args.Exception.Message, "Unhandled UI Exception", MessageBoxButton.OK, MessageBoxImage.Error);
            args.Handled = true;
        };

        AppDomain.CurrentDomain.UnhandledException += (s, args) =>
        {
            if (args.ExceptionObject is Exception ex)
            {
                MessageBox.Show(ex.Message, "Unhandled Non-UI Exception", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        };
    }

    private async Task RunMartingaleTests()
    {
        Console.WriteLine("🧪 Executando testes do sistema de Martingale...");

        try
        {
            var test = new MartingaleTest();
            await test.RunTests();
            Console.WriteLine("✅ Todos os testes foram executados!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Erro durante os testes: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }

        Console.WriteLine("\nPressione qualquer tecla para sair...");
        Console.ReadKey();
    }
}
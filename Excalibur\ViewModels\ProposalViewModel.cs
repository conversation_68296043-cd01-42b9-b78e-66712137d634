using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using Excalibur.Core.Models.DTOs.ApiResponses;
using Excalibur.Core.Interfaces;
using System.Collections.ObjectModel;
using Microsoft.Extensions.Logging;
using Excalibur.Infrastructure.Commands;
using System.Diagnostics.CodeAnalysis;

namespace Excalibur.ViewModels;

public class ProposalViewModel : INotifyPropertyChanged, IDisposable
{
    private readonly IDerivApiService _derivApiService;
    private readonly ILogger<ProposalViewModel> _logger;
    private ContractType? _selectedContract;
    private string _selectedSymbol = string.Empty;
    
    // Campos base
    private decimal _stake = 0.35m;
    private string _stakeText = "0.35";
    private decimal _originalStake = 0.35m; // Stake original para simulação (não afetado pelo martingale)
    private string _duration = "5";
    private string _durationType = "t"; // t = ticks, s = seconds, m = minutes, h = hours, d = days
    private decimal? _barrier;
    private int? _digit;
    private decimal _payout;
    private decimal _payoutPerPoint;
    private decimal _strikePrice;
    private decimal _currentSpot;
    private string? _currentProposalId;
    
    // Propriedades de visibilidade
    private bool _showBarrierField;
    private bool _showDigitField;
    private bool _isLoading;
    private string _contractDescription = string.Empty;
    private string _barrierText = string.Empty;
    private readonly ObservableCollection<string> _barrierOptions = new();

    // Guarda o ID da proposta usado na última compra, até recebermos o contract_id real
    private string? _pendingPurchaseProposalId;
    // Informações da compra pendente — usadas para criar a linha somente quando o transaction_id chegar
    private string? _pendingContractType;
    private decimal _pendingStartSpot;
    private decimal _pendingStake;
    private decimal _pendingPayout;
    private int _pendingDuration;
    private string _pendingDurationType = string.Empty;

    // Throttling para prevenir múltiplas proposals simultâneas
    private readonly SemaphoreSlim _proposalSemaphore = new SemaphoreSlim(1, 1);
    private DateTime _lastProposalRequest = DateTime.MinValue;
    private const int MIN_PROPOSAL_INTERVAL_MS = 500; // Mínimo 500ms entre proposals
    
    // Sistema de simulação automática
    private System.Timers.Timer? _autoSimulationTimer;
    private bool _isAutoSimulating = false;
    private bool _isContinuous = false;
    private const int AUTO_SIMULATION_CHECK_INTERVAL_MS = 1000; // Verificar a cada 1000ms para reduzir processamento
    private DateTime? _lastSimulationTime = null;
    private int _lastSimulationDurationInSeconds = 0;
    private bool _isCreatingSimulation = false; // Prevenir criação simultânea
    
    // Save configuration property
    private bool _saveConfiguration = false;

    public ProposalViewModel(IDerivApiService derivApiService, ILogger<ProposalViewModel> logger)
    {
        _derivApiService = derivApiService;
        _logger = logger;
        CalculateProposalCommand = new AsyncCommand(ExecuteCalculateProposal);
        BuyCommand = new RelayCommand(async () => await ExecuteBuyContract(), () => CanBuy);
        SimulateCommand = new RelayCommand(async () => await ExecuteSimulateContract(), () => CanBuy);
        
        // Subscrever ao evento de resposta de proposta
        _derivApiService.ProposalReceived += OnProposalReceived;
        _derivApiService.TickReceived += OnTickReceived;
        _derivApiService.ErrorOccurred += OnErrorOccurred;
        _derivApiService.BuyCompleted += OnBuyCompleted;
        
        // Inicializar valores padrão
        UpdateFieldVisibility();
        
        // Carregar configurações salvas se disponíveis
        LoadSettings();
    }

    // Propriedades públicas
    public ContractType? SelectedContract
    {
        get => _selectedContract;
        set
        {
            _logger.LogInformation("[PROPOSAL-VM] Contrato selecionado: {ContractDisplay} ({ContractType})", 
                value?.ContractDisplay ?? "null", value?.ContractTypeValue ?? "null");
            
            _selectedContract = value;
            OnPropertyChanged();
            UpdateFieldVisibility();
            UpdateContractDescription();
            
            // Log detalhado do contrato selecionado
            if (_selectedContract != null)
            {
                _logger.LogInformation("[PROPOSAL-VM] Detalhes do contrato:");
                _logger.LogInformation("  ContractDisplay: {ContractDisplay}", _selectedContract.ContractDisplay);
                _logger.LogInformation("  ContractTypeValue: {ContractTypeValue}", _selectedContract.ContractTypeValue);
                _logger.LogInformation("  ShowBarrierField: {ShowBarrierField}", ShowBarrierField);
                _logger.LogInformation("  ShowDigitField: {ShowDigitField}", ShowDigitField);
                _logger.LogInformation("  BarrierChoices: {BarrierChoices}", _selectedContract.BarrierChoices != null ? string.Join(", ", _selectedContract.BarrierChoices) : "null");
            }
            
            // Para contratos Vanilla, aplicar defaults mas NÃO executar proposta automaticamente
            if (_selectedContract?.ContractTypeValue?.Contains("vanilla", System.StringComparison.OrdinalIgnoreCase) == true)
            {
                _logger.LogInformation("[PROPOSAL-VM] Aplicando defaults para contrato Vanilla");
                _stake = 0.8m;
                StakeText = "0.8";

                Duration = "1";
                DurationType = "m";

                if (_selectedContract?.BarrierChoices?.Contains("+0.000") == true)
                {
                    BarrierText = "+0.000";
                }
                
                // Para Vanilla, aguardar que o usuário configure todos os parâmetros
                // Não executar automaticamente
                _logger.LogInformation("[PROPOSAL-VM] Contrato Vanilla configurado, aguardando parâmetros do usuário");
                return;
            }
            
            // Para outros contratos, executar automaticamente
            _ = ExecuteCalculateProposal();
        }
    }

    public string SelectedSymbol
    {
        get => _selectedSymbol;
        set
        {
            _logger.LogInformation("[PROPOSAL-VM] Símbolo selecionado: {Symbol}", value ?? "null");
            _selectedSymbol = value;
            OnPropertyChanged();
            // Iniciar (ou reforçar) stream de ticks para o símbolo selecionado
            if (!string.IsNullOrWhiteSpace(_selectedSymbol))
                _ = _derivApiService.RequestTicksStreamAsync(_selectedSymbol);
            _ = ExecuteCalculateProposal();
        }
    }

    public string StakeText
    {
        get => _stakeText;
        set
        {
            _stakeText = value;
            if (decimal.TryParse(value.Replace(',', '.'), System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out var parsed))
            {
                _stake = Math.Max(0.35m, parsed);
                _originalStake = _stake; // Atualizar stake original sempre que o usuário altera o valor
                _logger.LogInformation("[PROPOSAL] StakeText alterado: {Value} -> Parsed: {Parsed} -> Final: {Stake}", value, parsed, _stake);
                _ = ExecuteCalculateProposal();
            }
            else
            {
                _logger.LogWarning("[PROPOSAL] StakeText inválido: {Value}, mantendo valor atual: {CurrentStake}", value, _stake);
            }
            OnPropertyChanged();
        }
    }

    public string Duration
    {
        get => _duration;
        set
        {
            _duration = value;
            OnPropertyChanged();
            _ = ExecuteCalculateProposal();
        }
    }

    public string DurationType
    {
        get => _durationType;
        set
        {
            _durationType = value;
            OnPropertyChanged();
            _ = ExecuteCalculateProposal();
        }
    }

    public decimal? Barrier
    {
        get => _barrier;
        set
        {
            _barrier = value;
            OnPropertyChanged();
            _ = ExecuteCalculateProposal();
        }
    }

    public int? Digit
    {
        get => _digit;
        set
        {
            if (value.HasValue)
                _digit = Math.Max(0, Math.Min(9, value.Value));
            else
                _digit = value;
            OnPropertyChanged();
            _ = ExecuteCalculateProposal();
        }
    }

    public decimal Payout
    {
        get => _payout;
        set
        {
            _payout = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanBuy));
            BuyCommand.RaiseCanExecuteChanged();
        }
    }

    public bool ShowBarrierField
    {
        get => _showBarrierField;
        set
        {
            _showBarrierField = value;
            OnPropertyChanged();
        }
    }

    public bool ShowDigitField
    {
        get => _showDigitField;
        set
        {
            _showDigitField = value;
            OnPropertyChanged();
        }
    }

    public bool IsLoading
    {
        get => _isLoading;
        set
        {
            _isLoading = value;
            OnPropertyChanged();
            OnPropertyChanged(nameof(CanBuy));
            BuyCommand.RaiseCanExecuteChanged();
        }
    }

    public string ContractDescription
    {
        get => _contractDescription;
        set
        {
            _contractDescription = value;
            OnPropertyChanged();
        }
    }

    // Propriedade somente leitura para outras lógicas
    public decimal Stake => _stake;
    
    // Propriedade para stake original (não afetado pelo martingale) - usado pela simulação
    public decimal OriginalStake => _originalStake;

    public void SetStake(decimal stake)
    {
        System.Diagnostics.Debug.WriteLine($"[PROPOSAL] 💰 SetStake chamado! Novo stake: {stake} (Original: {_originalStake})");
        _stake = stake;
        _stakeText = stake.ToString("F2");
        OnPropertyChanged(nameof(Stake));
        OnPropertyChanged(nameof(StakeText));
        System.Diagnostics.Debug.WriteLine($"[PROPOSAL] 💰 Stake atualizado para: {_stake} (Original mantido: {_originalStake})");
        
        // Disparar nova proposta com o novo stake
        _ = ExecuteCalculateProposal();
    }

    public void ResetStakeToOriginal(decimal originalStake)
    {
        System.Diagnostics.Debug.WriteLine($"[PROPOSAL] 🔄 ResetStakeToOriginal chamado! Original: {originalStake}");
        _stake = originalStake;
        _stakeText = originalStake.ToString("F2");
        OnPropertyChanged(nameof(Stake));
        OnPropertyChanged(nameof(StakeText));
        System.Diagnostics.Debug.WriteLine($"[PROPOSAL] ✅ Stake resetado para valor original: {_stake}");
        
        // Disparar nova proposta com o stake resetado
        _ = ExecuteCalculateProposal();
    }

    public string BarrierText
    {
        get => _barrierText;
        set
        {
            _barrierText = value;
            if (decimal.TryParse(value.Replace(',', '.'), System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out var parsed))
            {
                Barrier = parsed;
            }
            OnPropertyChanged();
            
            // Executar proposta automaticamente quando barreira é alterada
            if (!string.IsNullOrWhiteSpace(value) && SelectedContract != null)
            {
                _logger.LogInformation("[PROPOSAL-VM] Barreira alterada para: {BarrierText}, executando proposta", value);
                _ = ExecuteCalculateProposal();
            }
        }
    }

    public ObservableCollection<string> BarrierOptions => _barrierOptions;

    public decimal CurrentSpot
    {
        get => _currentSpot;
        set
        {
            _currentSpot = value;
            OnPropertyChanged();
        }
    }

    public bool IsVanillaContract => 
        SelectedContract?.ContractTypeValue?.Contains("vanilla", StringComparison.OrdinalIgnoreCase) == true;

    public string PayoutPerPointText => 
        IsVanillaContract && _payoutPerPoint > 0 ? $"Payout per point: {_payoutPerPoint:F2}" : string.Empty;

    public string StrikePriceText => 
        IsVanillaContract && _strikePrice > 0 ? $"Strike: {_strikePrice:F5}" : string.Empty;

    public bool CanBuy => !string.IsNullOrEmpty(_currentProposalId) && Payout > 0 && !IsLoading;

    // Propriedades para simulação automática
    public bool IsAutoSimulating 
    { 
        get => _isAutoSimulating; 
        private set 
        { 
            _isAutoSimulating = value; 
            OnPropertyChanged(); 
            OnPropertyChanged(nameof(SimulateButtonText));
            OnPropertyChanged(nameof(SimulateButtonColor));
            SimulateCommand.RaiseCanExecuteChanged();
        } 
    }

    public string SimulateButtonText => IsAutoSimulating ? "STOP" : "SIMULATE";
    public string SimulateButtonColor => IsAutoSimulating ? "#FFD32F2F" : "#FF0078D4";

    public bool IsContinuous
    {
        get => _isContinuous;
        set
        {
            if (_isContinuous != value)
            {
                _isContinuous = value;
                _logger.LogInformation("[SIMULATE] Toggle 'Manter' alterado para: {State}", value ? "ATIVADO" : "DESATIVADO");
                OnPropertyChanged();
            }
        }
    }

    public bool SaveConfiguration
    {
        get => _saveConfiguration;
        set
        {
            _saveConfiguration = value;
            OnPropertyChanged();
            
            if (value)
            {
                SaveSettings();
            }
        }
    }

    // Comandos
    public ICommand CalculateProposalCommand { get; }
    public RelayCommand BuyCommand { get; }
    public RelayCommand SimulateCommand { get; }

    private void UpdateFieldVisibility()
    {
        if (SelectedContract == null)
        {
            ShowBarrierField = false;
            ShowDigitField = false;
            return;
        }

        var contractType = SelectedContract.ContractTypeValue.ToLower();
        var contractDisplay = SelectedContract.ContractDisplay.ToLower();
        
        System.Diagnostics.Debug.WriteLine($"[PROPOSAL] Contract type: {contractType}, Display: {contractDisplay}");
        
        // Contratos que precisam de barreira (HIGHER/LOWER, TOUCH/NOTOUCH, VANILLA)
        ShowBarrierField = contractType.Contains("higher") ||
                          contractType.Contains("lower") ||
                          contractType.Contains("touch") ||
                          contractType.Contains("notouch") ||
                          contractType.Contains("vanilla");

        // Contratos que precisam de dígito (0-9)
        ShowDigitField = contractDisplay.Contains("digit") ||
                        contractDisplay.Contains("over") ||
                        contractDisplay.Contains("under") ||
                        contractDisplay.Contains("matches") ||
                        contractDisplay.Contains("differs");

        System.Diagnostics.Debug.WriteLine($"[PROPOSAL] ShowBarrierField: {ShowBarrierField}, ShowDigitField: {ShowDigitField}");

        // Se o campo deixar de ser necessário, limpar valores
        if (!ShowBarrierField)
        {
            Barrier = null;
            _barrierOptions.Clear();
            BarrierText = string.Empty;
        }
        else
        {
            // Carregar opções de barreira se necessário
            UpdateBarrierOptions();
        }
        
        if (!ShowDigitField)
        {
            Digit = null;
        }
    }

    private void UpdateContractDescription()
    {
        if (SelectedContract == null)
        {
            ContractDescription = "Selecione um contrato";
            return;
        }

        ContractDescription = $"{SelectedContract.ContractDisplay} - {SelectedContract.ContractCategoryDisplay}";
    }

    private async Task ExecuteCalculateProposal()
    {
        // Throttling: verificar intervalo mínimo entre proposals
        var timeSinceLastRequest = DateTime.Now - _lastProposalRequest;
        if (timeSinceLastRequest.TotalMilliseconds < MIN_PROPOSAL_INTERVAL_MS)
        {
            _logger.LogDebug("[PROPOSAL-VM] Proposal throttled - too soon since last request");
            return;
        }

        // Semáforo para prevenir múltiplas execuções simultâneas
        if (!await _proposalSemaphore.WaitAsync(100))
        {
            _logger.LogDebug("[PROPOSAL-VM] Proposal skipped - another proposal in progress");
            return;
        }

        try
        {
            if (SelectedContract == null || string.IsNullOrEmpty(SelectedSymbol) || !_derivApiService.IsConnected)
            {
                _logger.LogWarning("[PROPOSAL-VM] Condições não atendidas para calcular proposta: Contract={ContractDisplay}, Symbol={Symbol}, Connected={Connected}", 
                    SelectedContract?.ContractDisplay ?? "null", SelectedSymbol ?? "null", _derivApiService.IsConnected);
                return;
            }

            _lastProposalRequest = DateTime.Now;
            IsLoading = true;

            if (!int.TryParse(Duration, out var durationValue) || durationValue <= 0)
            {
                _logger.LogWarning("[PROPOSAL-VM] Duração inválida: {Duration}", Duration);
                IsLoading = false;
                return;
            }

            // Se barreira é exigida mas não fornecida, aguardar input do usuário
            if (ShowBarrierField && string.IsNullOrWhiteSpace(BarrierText))
            {
                _logger.LogInformation("[PROPOSAL-VM] Aguardando barreira do usuário (ShowBarrierField=true, BarrierText='{BarrierText}')", BarrierText ?? "null");
                IsLoading = false;
                
                // Para contratos Vanilla, não é um erro - é esperado que o usuário configure
                if (SelectedContract?.ContractTypeValue?.Contains("vanilla", System.StringComparison.OrdinalIgnoreCase) == true)
                {
                    _logger.LogInformation("[PROPOSAL-VM] Contrato Vanilla aguardando configuração de barreira pelo usuário");
                }
                return;
            }

            if (ShowDigitField && !Digit.HasValue)
            {
                _logger.LogInformation("[PROPOSAL-VM] Aguardando dígito do usuário (ShowDigitField=true, Digit={Digit})", Digit);
                IsLoading = false;
                return;
            }

            string? barrierParam = null;
            if (ShowDigitField && Digit.HasValue)
            {
                barrierParam = Digit.Value.ToString(System.Globalization.CultureInfo.InvariantCulture);
            }
            else if (ShowBarrierField && !string.IsNullOrWhiteSpace(BarrierText))
            {
                barrierParam = BarrierText.Trim();
            }

            _logger.LogInformation("[PROPOSAL-VM] Solicitando proposta: Symbol={Symbol}, Contract={Contract}, Stake={Stake}, Duration={Duration}, Unit={Unit}, Barrier={Barrier}", 
                SelectedSymbol, SelectedContract.ContractTypeValue, Stake, durationValue, DurationType, barrierParam ?? "null");

            await _derivApiService.RequestProposalAsync(
                SelectedSymbol,
                SelectedContract.ContractTypeValue,
                Stake,
                durationValue,
                DurationType,
                barrierParam
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PROPOSAL-VM] Erro ao calcular proposta");
            Payout = 0;
            IsLoading = false;
        }
        finally
        {
            _proposalSemaphore.Release();
        }
    }

    private async Task ExecuteBuyContract()
    {
        if (string.IsNullOrEmpty(_currentProposalId))
        {
            _logger.LogWarning("[BUY] Tentativa de compra sem ID de proposta válido");
            return;
        }

        if (!_derivApiService.IsConnected)
        {
            _logger.LogWarning("[BUY] Tentativa de compra sem conexão ativa");
            return;
        }

        try
        {
            // Armazenar o proposalId usado nesta compra
            var buyProposalId = _currentProposalId;
            _pendingPurchaseProposalId = buyProposalId;

            _logger.LogInformation("[BUY] Executando compra: ProposalId={ProposalId}, Price={Price}", 
                buyProposalId, Payout);

            await _derivApiService.BuyContractAsync(buyProposalId, Payout);
            
            // Log de confirmação
            _logger.LogInformation("[BUY] Solicitação de compra enviada com sucesso");
            
            // CORREÇÃO: Armazenar informações da compra para criar a transação somente após receber o Transaction ID
            _pendingContractType   = SelectedContract?.ContractDisplay ?? "Unknown";
            _pendingStartSpot      = CurrentSpot;
            _pendingStake          = Stake; // Usar Stake atual (será corrigido no método principal)
            _pendingPayout         = Payout;
            _pendingDuration       = int.TryParse(Duration, out var durTmp) ? durTmp : 5;
            _pendingDurationType   = DurationType;
            
            // Após compra, aguardar um pouco antes de solicitar nova proposta
            _logger.LogInformation("[BUY] Agendando nova proposta após compra");
            _ = Task.Delay(1000).ContinueWith(async _ => 
            {
                try
                {
                    await ExecuteCalculateProposal();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error requesting new proposal after buy");
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[BUY] Erro ao executar compra");
        }
    }

    public async Task<string> ExecuteRealPurchaseAsync(decimal? customStake = null)
    {
        var effectiveStake = customStake ?? Stake;

        _logger.LogInformation("[BUY] 🎯 ExecuteRealPurchaseAsync INICIADO! Stake={Stake}, CustomStake={CustomStake}, EffectiveStake={EffectiveStake}",
            Stake, customStake, effectiveStake);

        System.Diagnostics.Debug.WriteLine($"[MARTINGALE-BUY] 🎯 ExecuteRealPurchaseAsync INICIADO!");
        System.Diagnostics.Debug.WriteLine($"[MARTINGALE-BUY] 🎯 CurrentProposalId: {_currentProposalId}");
        System.Diagnostics.Debug.WriteLine($"[MARTINGALE-BUY] 🎯 IsConnected: {_derivApiService.IsConnected}");
        System.Diagnostics.Debug.WriteLine($"[MARTINGALE-BUY] 🎯 Stake: {Stake}, CustomStake: {customStake}, EffectiveStake: {effectiveStake}");
        
        if (string.IsNullOrEmpty(_currentProposalId))
        {
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE-BUY] ❌ Sem ID de proposta válido! Tentando gerar nova proposta...");
            _logger.LogWarning("[BUY] Tentativa de compra real sem ID de proposta válido. Aguardando nova proposta...");
            
            // Tentar gerar nova proposta e aguardar
            await ExecuteCalculateProposal();
            await Task.Delay(1000); // Aguardar proposta ser gerada
            
            if (string.IsNullOrEmpty(_currentProposalId))
            {
                _logger.LogError("[BUY] Falha ao gerar proposta válida para compra real");
                return string.Empty;
            }
            
            _logger.LogInformation("[BUY] Nova proposta gerada com sucesso: {ProposalId}", _currentProposalId);
        }

        if (!_derivApiService.IsConnected)
        {
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE-BUY] ❌ Sem conexão ativa!");
            _logger.LogWarning("[BUY] Tentativa de compra real sem conexão ativa");
            return string.Empty;
        }

        try
        {
            var buyProposalId = _currentProposalId;
            _pendingPurchaseProposalId = buyProposalId;

            _logger.LogInformation("[BUY] Executando compra real via martingale: ProposalId={ProposalId}, Price={Price}", 
                buyProposalId, Payout);

            // CORREÇÃO: Criar TaskCompletionSource para aguardar contract ID real da API
            var tcs = new TaskCompletionSource<string>();
            
            // Handler temporário para capturar o contract ID real
            EventHandler<string>? buyCompletedHandler = null;
            buyCompletedHandler = (sender, message) =>
            {
                try
                {
                    var realContractId = ExtractContractIdFromMessage(message);
                    _logger.LogInformation("[BUY] Contract ID extraído da mensagem: {ContractId}", realContractId);
                    
                    if (!string.IsNullOrEmpty(realContractId) && 
                        _pendingPurchaseProposalId == buyProposalId)
                    {
                        _logger.LogInformation("[BUY] Contract ID real capturado: {ContractId}", realContractId);
                        tcs.SetResult(realContractId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[BUY] Erro ao extrair contract ID da mensagem");
                    tcs.SetException(ex);
                }
                finally
                {
                    // Remover handler temporário
                    if (buyCompletedHandler != null)
                        _derivApiService.BuyCompleted -= buyCompletedHandler;
                }
            };
            
            // Registrar handler temporário
            _derivApiService.BuyCompleted += buyCompletedHandler;

            await _derivApiService.BuyContractAsync(buyProposalId, Payout);
            
            _logger.LogInformation("[BUY] Aguardando contract ID real da API...");
            
            // Aguardar resposta da API (com timeout de 10 segundos)
            var realContractId = await tcs.Task.WaitAsync(TimeSpan.FromSeconds(10));
            
            _logger.LogInformation("[BUY] Contract ID real recebido: {ContractId}", realContractId);
            
            // CORREÇÃO: Armazenar informações da compra com stake correto
            _pendingContractType   = SelectedContract?.ContractDisplay ?? "Unknown";
            _pendingStartSpot      = CurrentSpot;
            _pendingStake          = effectiveStake; // CORREÇÃO: Usar effectiveStake em vez de Stake
            _pendingPayout         = Payout;
            _pendingDuration       = int.TryParse(Duration, out var durTmp) ? durTmp : 5;
            _pendingDurationType   = DurationType;

            _logger.LogInformation("[BUY] 📊 Dados pendentes atualizados: Stake={Stake}, Payout={Payout}, ContractType={ContractType}",
                _pendingStake, _pendingPayout, _pendingContractType);
            
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE-BUY] ✅ Retornando Contract ID real: {realContractId}");
            
            // Solicitar nova proposta após compra
            _ = Task.Delay(1000).ContinueWith(async _ => 
            {
                try
                {
                    await ExecuteCalculateProposal();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error requesting new proposal after real purchase");
                }
            });
            
            return realContractId; // CORREÇÃO: Retornar contract ID real em vez de proposal ID
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[BUY] Erro ao executar compra real via martingale");
            System.Diagnostics.Debug.WriteLine($"[MARTINGALE-BUY] ❌ Erro: {ex.Message}");
            return string.Empty;
        }
    }

    private async Task ExecuteSimulateContract()
    {
        if (IsAutoSimulating)
        {
            // Parar simulação automática
            StopAutoSimulation();
            return;
        }

        if (SelectedContract == null || string.IsNullOrEmpty(SelectedSymbol))
        {
            _logger.LogWarning("[SIMULATE] Contrato ou símbolo não selecionado");
            return;
        }

        try
        {
            _logger.LogInformation("[SIMULATE] Iniciando simulação automática: ContractType={ContractType}, Symbol={Symbol}, Stake={Stake}, Duration={Duration}",
                SelectedContract.ContractDisplay, SelectedSymbol, Stake, Duration);

            StartAutoSimulation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[SIMULATE] Erro ao iniciar simulação automática");
        }
    }

    private void StartAutoSimulation()
    {
        if (IsAutoSimulating)
            return;

        _logger.LogInformation("[SIMULATE] Iniciando simulação automática contínua");
        
        // Limpar tabelas antes de iniciar nova simulação
        _logger.LogInformation("[SIMULATE] Solicitando limpeza das tabelas Simulação e Compras");
        _logger.LogInformation("[SIMULATE] Toggle 'Manter' está: {IsContinuous}", IsContinuous ? "ATIVADO" : "DESATIVADO");
        ClearTablesRequested?.Invoke(this, EventArgs.Empty);
        
        IsAutoSimulating = true;
        
        // Configurar timer para verificar se pode criar nova simulação
        _autoSimulationTimer = new System.Timers.Timer(AUTO_SIMULATION_CHECK_INTERVAL_MS);
        _autoSimulationTimer.Elapsed += async (sender, e) =>
        {
            try
            {
                await CheckAndExecuteSimulation();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[SIMULATE] Erro durante verificação de simulação automática");
            }
        };
        
        _autoSimulationTimer.AutoReset = true;
        _autoSimulationTimer.Start();
        
        _logger.LogInformation("[SIMULATE] Timer de simulação automática iniciado. Primeira simulação será criada em breve.");
    }

    private void StopAutoSimulation()
    {
        if (!IsAutoSimulating)
            return;

        _logger.LogInformation("[SIMULATE] Parando simulação automática");
        
        _autoSimulationTimer?.Stop();
        _autoSimulationTimer?.Dispose();
        _autoSimulationTimer = null;
        
        // Reset do controle de tempo
        _lastSimulationTime = null;
        _lastSimulationDurationInSeconds = 0;
        _isCreatingSimulation = false;
        
        IsAutoSimulating = false;
    }

    private async Task CheckAndExecuteSimulation()
    {
        if (!IsAutoSimulating || SelectedContract == null || string.IsNullOrEmpty(SelectedSymbol))
            return;

        // Prevenir criação simultânea de simulações
        if (_isCreatingSimulation)
        {
            _logger.LogDebug("[SIMULATE] Já está criando uma simulação, aguardando...");
            return;
        }

        var now = DateTime.Now;

        // Verificar se a simulação anterior já expirou
        if (_lastSimulationTime.HasValue)
        {
            var expectedExpirationTime = _lastSimulationTime.Value.AddSeconds(_lastSimulationDurationInSeconds);
            
            // Só criar nova simulação se chegou ou passou do tempo exato de expiração
            // Adicionar margem de 500ms para evitar sobreposição
            if (now < expectedExpirationTime.AddMilliseconds(500))
            {
                var timeRemaining = expectedExpirationTime - now;
                _logger.LogDebug("[SIMULATE] Aguardando expiração da simulação anterior. Tempo restante: {TimeRemaining:F1}s", timeRemaining.TotalSeconds);
                return;
            }
            
            // Aguardar mais tempo se a simulação expirou recentemente para evitar múltiplas criações
            if (now < expectedExpirationTime.AddSeconds(1))
            {
                _logger.LogDebug("[SIMULATE] Aguardando período de segurança após expiração");
                return;
            }
            
            _logger.LogInformation("[SIMULATE] Simulação anterior expirou às {ExpirationTime}. Criando nova simulação às {NewTime}.", 
                expectedExpirationTime.ToString("HH:mm:ss.fff"), now.ToString("HH:mm:ss.fff"));
        }
        else
        {
            _logger.LogInformation("[SIMULATE] Primeira simulação automática será criada.");
        }

        await ExecuteSingleSimulation();
    }

    private async Task ExecuteSingleSimulation()
    {
        if (!IsAutoSimulating || SelectedContract == null || string.IsNullOrEmpty(SelectedSymbol))
            return;

        // Verificar novamente se já está criando uma simulação
        if (_isCreatingSimulation)
        {
            _logger.LogDebug("[SIMULATE] Já está criando uma simulação, cancelando operação duplicada");
            return;
        }

        // Marcar que está criando simulação
        _isCreatingSimulation = true;

        try
        {
            // Aguardar proposta válida se necessário
            if (string.IsNullOrEmpty(_currentProposalId) || Payout <= 0)
            {
                _logger.LogDebug("[SIMULATE] Aguardando proposta válida para simulação automática");
                await ExecuteCalculateProposal();
                
                // Aguardar um pouco para a proposta chegar
                await Task.Delay(1000);
                
                if (string.IsNullOrEmpty(_currentProposalId) || Payout <= 0)
                {
                    _logger.LogWarning("[SIMULATE] Proposta ainda não válida, pulando simulação");
                    return;
                }
            }

            var durationValue = int.TryParse(Duration, out var durValue) ? durValue : 5;
            var durationInSeconds = CalculateDurationInSeconds(durationValue, DurationType);

            // Determinar o tempo de início da simulação
            DateTime simulationStartTime;
            
            if (_lastSimulationTime.HasValue)
            {
                // Se há simulação anterior, começar exatamente quando ela expira
                var expectedExpirationTime = _lastSimulationTime.Value.AddSeconds(_lastSimulationDurationInSeconds);
                simulationStartTime = expectedExpirationTime;
                
                _logger.LogInformation("[SIMULATE] Nova simulação iniciará às {StartTime} (expiração da anterior)", 
                    simulationStartTime.ToString("HH:mm:ss.fff"));
            }
            else
            {
                // Primeira simulação - usar tempo atual
                simulationStartTime = DateTime.Now;
                _logger.LogInformation("[SIMULATE] Primeira simulação iniciará às {StartTime}", 
                    simulationStartTime.ToString("HH:mm:ss.fff"));
            }

            // Registrar tempo da simulação para controle preciso
            _lastSimulationTime = simulationStartTime;
            _lastSimulationDurationInSeconds = durationInSeconds;

            _logger.LogInformation("[SIMULATE] Executando simulação automática: ContractType={ContractType}, Symbol={Symbol}, Stake={Stake}, Duration={Duration}{DurationType} ({DurationSeconds}s)",
                SelectedContract.ContractDisplay, SelectedSymbol, Stake, durationValue, DurationType, durationInSeconds);
            
            _logger.LogInformation("[SIMULATE] Tempo de início: {SimulationTime}, Expiração prevista: {ExpirationTime}", 
                simulationStartTime.ToString("HH:mm:ss.fff"), 
                simulationStartTime.AddSeconds(durationInSeconds).ToString("HH:mm:ss.fff"));

            // Gerar um ID único para a transação simulada
            var simulatedContractId = $"SIM_{simulationStartTime:yyyyMMddHHmmss}_{Random.Shared.Next(1000, 9999)}";

            // Criar evento de simulação
            System.Diagnostics.Debug.WriteLine($"[PROPOSAL] ======= DISPARANDO EVENTO ContractSimulated =======");
            System.Diagnostics.Debug.WriteLine($"[PROPOSAL] ContractId: {simulatedContractId}");
            System.Diagnostics.Debug.WriteLine($"[PROPOSAL] StartTime: {simulationStartTime:HH:mm:ss.fff}");
            System.Diagnostics.Debug.WriteLine($"[PROPOSAL] Listeners: {ContractSimulated?.GetInvocationList()?.Length ?? 0}");
            
            ContractSimulated?.Invoke(this, new ContractSimulatedEventArgs
            {
                ContractType = SelectedContract.ContractDisplay,
                ContractId = simulatedContractId,
                StartSpot = CurrentSpot,
                Stake = Stake,
                Payout = Payout,
                Duration = durationValue,
                DurationType = DurationType
            });
            
            System.Diagnostics.Debug.WriteLine($"[PROPOSAL] ======= EVENTO DISPARADO =======");

            _logger.LogInformation("[SIMULATE] Simulação automática criada: ID={ContractId}, Duração={Duration}s", simulatedContractId, durationInSeconds);
            
            // Solicitar nova proposta para próxima simulação
            _ = Task.Delay(500).ContinueWith(async _ => 
            {
                try
                {
                    if (IsAutoSimulating)
                    {
                        await ExecuteCalculateProposal();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "[SIMULATE] Erro ao solicitar nova proposta");
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[SIMULATE] Erro durante execução de simulação individual");
        }
        finally
        {
            // Garantir que o flag seja resetado e adicionar pequeno delay para evitar sobreposição
            await Task.Delay(100);
            _isCreatingSimulation = false;
        }
    }

    private int CalculateDurationInSeconds(int duration, string durationType)
    {
        return durationType?.ToLower() switch
        {
            "t" => duration * 2, // cada tick = 2 segundos
            "s" => duration, // segundos
            "m" => duration * 60, // minutos
            "h" => duration * 3600, // horas
            "d" => duration * 86400, // dias
            _ => duration * 2 // default para ticks
        };
    }

    private void OnErrorOccurred(object? sender, string error)
    {
        _logger.LogError("[PROPOSAL-VM] Erro recebido da API: {Error}", error);
        
        // Se é erro de rate limiting, não parar a simulação - apenas aguardar
        if (error.Contains("rate limit", StringComparison.OrdinalIgnoreCase))
        {
            _logger.LogWarning("[PROPOSAL-VM] Rate limit detectado - aguardando antes de tentar novamente");
            return; // Não parar simulação por rate limiting
        }
        
        System.Windows.Application.Current?.Dispatcher.Invoke(() =>
        {
            IsLoading = false;
            Payout = 0;
        });
    }

    private void OnBuyCompleted(object? sender, string message)
    {
        _logger.LogInformation("[BUY] Compra concluída: {Message}", message);
        
        System.Windows.Application.Current?.Dispatcher.Invoke(() =>
        {
            var contractId = ExtractContractIdFromMessage(message);
            
            if (!string.IsNullOrEmpty(contractId) && !string.IsNullOrEmpty(_pendingPurchaseProposalId))
            {
                _logger.LogInformation("[BUY] Disparando evento ContractPurchased: ContractId={ContractId}, ContractType={ContractType}", 
                    contractId, _pendingContractType ?? "Unknown");
                
                // Verificar se há listeners para o evento
                var listeners = ContractPurchased?.GetInvocationList()?.Length ?? 0;
                _logger.LogInformation("[BUY] Listeners para ContractPurchased: {Listeners}", listeners);
                
                // CORREÇÃO: Log dos dados antes de disparar o evento
                _logger.LogInformation("[BUY] 📊 Dados do evento ContractPurchased: ContractId={ContractId}, Stake={Stake}, Payout={Payout}",
                    contractId, _pendingStake, _pendingPayout);

                // Criar a transação agora que temos o Transaction ID real
                ContractPurchased?.Invoke(this, new ContractPurchasedEventArgs
                {
                    ContractType = _pendingContractType ?? "Unknown",
                    ContractId   = contractId,
                    StartSpot    = _pendingStartSpot,
                    Stake        = _pendingStake,
                    Payout       = _pendingPayout,
                    Duration     = _pendingDuration,
                    DurationType = _pendingDurationType
                });

                _logger.LogInformation("[BUY] Evento ContractPurchased disparado com sucesso!");

                // Limpar dados pendentes
                _pendingPurchaseProposalId = null;
                _pendingContractType = null;
                _pendingDurationType = string.Empty;
            }
            else
            {
                _logger.LogWarning("[BUY] Não foi possível disparar ContractPurchased: ContractId={ContractId}, PendingId={PendingId}", 
                    contractId ?? "null", _pendingPurchaseProposalId ?? "null");
            }
            
            _logger.LogInformation("[BUY] Feedback para usuário: {Message}", message);
        });
    }
    
    private string ExtractContractIdFromMessage(string message)
    {
        try
        {
            _logger.LogInformation("[BUY] Extraindo Contract ID da mensagem: {Message}", message);
            
            // Extrair ID do formato "Compra realizada! ID: {id}, Preço: ..."
            var idIndex = message.IndexOf("ID: ");
            if (idIndex >= 0)
            {
                var startIndex = idIndex + 4; // "ID: ".Length
                var endIndex = message.IndexOf(",", startIndex);
                if (endIndex >= 0)
                {
                    var extractedId = message.Substring(startIndex, endIndex - startIndex).Trim();
                    _logger.LogInformation("[BUY] Contract ID extraído: {ContractId}", extractedId);
                    return extractedId;
                }
                else
                {
                    _logger.LogWarning("[BUY] Não foi possível encontrar vírgula após ID");
                }
            }
            else
            {
                _logger.LogWarning("[BUY] Não foi possível encontrar 'ID: ' na mensagem");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[BUY] Erro ao extrair Contract ID da mensagem: {Message}", message);
        }
        
        return string.Empty;
    }

    private void OnProposalReceived(object? sender, Core.Models.DTOs.ApiResponses.ProposalResponse proposalResponse)
    {
        System.Windows.Application.Current?.Dispatcher.Invoke(() =>
        {
            _logger.LogInformation("[PROPOSAL-VM] Proposta recebida: Payout={Payout}, AskPrice={AskPrice}", 
                proposalResponse.Proposal.Payout, proposalResponse.Proposal.AskPrice);

            var details = proposalResponse.Proposal;
            var payout = details.Payout;
            
            // Armazenar ID da proposta para compra
            _currentProposalId = details.Id;
            OnPropertyChanged(nameof(CanBuy));
            BuyCommand.RaiseCanExecuteChanged();

            // Em Vanilla, o campo payout pode vir 0, mas "display_value" traz o valor real
            if (payout <= 0)
            {
                if (decimal.TryParse(details.DisplayValue, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out var parsedDisplay))
                {
                    payout = parsedDisplay;
                }
                else if (details.AskPrice > 0)
                {
                    payout = details.AskPrice;
                }
            }

            Payout = payout;
            IsLoading = false;
            OnPropertyChanged(nameof(CanBuy));

            // Para Vanillas: extrair payout per point e calcular strike
            var isVanilla = SelectedContract?.ContractTypeValue?.Contains("VANILLA", System.StringComparison.OrdinalIgnoreCase) == true ||
                           details.LongCode.Contains("payout per point", System.StringComparison.OrdinalIgnoreCase) ||
                           details.LongCode.Contains("Vanilla", System.StringComparison.OrdinalIgnoreCase);
            
            if (isVanilla)
            {
                _logger.LogInformation("[VANILLA] Processando contrato Vanilla:");
                _logger.LogInformation("  DisplayValue: {DisplayValue}", details.DisplayValue ?? "null");
                _logger.LogInformation("  DisplayNumberOfContracts: {DisplayNumberOfContracts}", details.DisplayNumberOfContracts ?? "null");
                _logger.LogInformation("  Multiplier: {Multiplier}", details.Multiplier);
                _logger.LogInformation("  Spot: {Spot}", details.Spot);
                _logger.LogInformation("  BarrierText: {BarrierText}", BarrierText ?? "null");

                // Extrair payout per point do campo correto
                _payoutPerPoint = 0;
                
                // 1. Tentar DisplayNumberOfContracts (campo principal para Vanilla)
                if (!string.IsNullOrEmpty(details.DisplayNumberOfContracts) &&
                    decimal.TryParse(details.DisplayNumberOfContracts, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out var contractsValue))
                {
                    _payoutPerPoint = contractsValue;
                    _logger.LogInformation("[VANILLA] PayoutPerPoint extraído de DisplayNumberOfContracts: {PayoutPerPoint}", _payoutPerPoint);
                }
                // 2. Fallback para Multiplier
                else if (details.Multiplier.HasValue && details.Multiplier.Value > 0)
                {
                    _payoutPerPoint = details.Multiplier.Value;
                    _logger.LogInformation("[VANILLA] PayoutPerPoint extraído de Multiplier: {PayoutPerPoint}", _payoutPerPoint);
                }

                // Calcular strike price (spot + barreira)
                var barrierOffset = 0m;
                if (!string.IsNullOrEmpty(BarrierText))
                {
                    var cleanBarrier = BarrierText.Replace("+", "").Replace(" ", "");
                    if (decimal.TryParse(cleanBarrier, System.Globalization.NumberStyles.Any, System.Globalization.CultureInfo.InvariantCulture, out var parsedBarrier))
                    {
                        barrierOffset = parsedBarrier;
                    }
                }
                
                _strikePrice = details.Spot + barrierOffset;
                
                _logger.LogInformation("[VANILLA] Strike calculado: {Spot} + {BarrierOffset} = {StrikePrice}", 
                    details.Spot, barrierOffset, _strikePrice);
                _logger.LogInformation("[VANILLA] PayoutPerPoint final: {PayoutPerPoint}", _payoutPerPoint);

                // Atualizar propriedades da UI
                OnPropertyChanged(nameof(PayoutPerPointText));
                OnPropertyChanged(nameof(StrikePriceText));

                // Garantir stream de ticks ativo
                _ = _derivApiService.RequestTicksStreamAsync(SelectedSymbol);
            }
        });
    }

    private void UpdateBarrierOptions()
    {
        _barrierOptions.Clear();

        if (SelectedContract == null)
            return;

        System.Diagnostics.Debug.WriteLine($"[PROPOSAL] Loading barrier options: High={SelectedContract.HighBarrier}, Low={SelectedContract.LowBarrier}, Barrier={SelectedContract.Barrier}, Barrier2={SelectedContract.Barrier2}");

        // Adicionar valores de barreira disponíveis na API
        if (!string.IsNullOrEmpty(SelectedContract.HighBarrier))
        {
            _barrierOptions.Add(SelectedContract.HighBarrier);
        }
        if (!string.IsNullOrEmpty(SelectedContract.LowBarrier) && SelectedContract.LowBarrier != SelectedContract.HighBarrier)
        {
            _barrierOptions.Add(SelectedContract.LowBarrier);
        }
        if (!string.IsNullOrEmpty(SelectedContract.Barrier) && 
            !_barrierOptions.Contains(SelectedContract.Barrier))
        {
            _barrierOptions.Add(SelectedContract.Barrier);
        }
        if (!string.IsNullOrEmpty(SelectedContract.Barrier2) && 
            !_barrierOptions.Contains(SelectedContract.Barrier2))
        {
            _barrierOptions.Add(SelectedContract.Barrier2);
        }

        if (SelectedContract.BarrierChoices != null)
        {
            foreach (var choice in SelectedContract.BarrierChoices)
            {
                if (!_barrierOptions.Contains(choice))
                    _barrierOptions.Add(choice);
            }
        }

        // Se temos opções, pré-selecionar a primeira
        if (_barrierOptions.Count > 0)
        {
            BarrierText = _barrierOptions[0];
        }

        System.Diagnostics.Debug.WriteLine($"[PROPOSAL] Loaded {_barrierOptions.Count} barrier options");
    }

    private void OnTickReceived(object? sender, TickData e)
    {
        if (e.Symbol != SelectedSymbol)
            return;

        // Atualizar spot atual na UI
        System.Windows.Application.Current?.Dispatcher.Invoke(() =>
        {
            CurrentSpot = e.Price;
        });

        // Apenas para contratos vanilla
        if (SelectedContract?.ContractTypeValue?.Contains("vanilla", System.StringComparison.OrdinalIgnoreCase) != true)
            return;

        // Verificar se temos payout per point configurado
        if (_payoutPerPoint <= 0)
        {
            _logger.LogDebug("[TICK] PayoutPerPoint não configurado: {PayoutPerPoint}", _payoutPerPoint);
            return;
        }

        var diff = 0m;
        if (SelectedContract.ContractTypeValue.Contains("call", System.StringComparison.OrdinalIgnoreCase))
        {
            diff = CurrentSpot - _strikePrice;
        }
        else if (SelectedContract.ContractTypeValue.Contains("put", System.StringComparison.OrdinalIgnoreCase))
        {
            diff = _strikePrice - CurrentSpot;
        }

        if (diff < 0)
            diff = 0;

        var dynamicPayout = diff * _payoutPerPoint;

        _logger.LogDebug("[TICK] Spot={Spot:F5}, Strike={Strike:F5}, Diff={Diff:F5}, Multiplier={Multiplier:F3}, Payout={Payout:F2}", 
            CurrentSpot, _strikePrice, diff, _payoutPerPoint, dynamicPayout);

        if (Math.Abs(dynamicPayout - Payout) > 0.01m)
        {
            System.Windows.Application.Current?.Dispatcher.Invoke(() =>
            {
                Payout = dynamicPayout;
            });
        }
    }

    // INotifyPropertyChanged
    public event PropertyChangedEventHandler? PropertyChanged;
    public event EventHandler<ContractPurchasedEventArgs>? ContractPurchased;
    public event EventHandler<ContractIdUpdatedEventArgs>? ContractIdUpdated;
    public event EventHandler<ContractSimulatedEventArgs>? ContractSimulated;
    public event EventHandler? ClearTablesRequested;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    private void SaveSettings()
    {
        try
        {
            var settings = Properties.Settings.Default;
            
            // Salvar configurações do Proposal
            settings.ProposalStakeText = StakeText;
            settings.ProposalDuration = Duration;
            settings.ProposalDurationType = DurationType;
            settings.ProposalBarrierText = BarrierText;
            settings.ProposalIsContinuous = IsContinuous;
            settings.ProposalSaveConfiguration = SaveConfiguration;
            
            settings.Save();
            
            _logger.LogInformation("[PROPOSAL] Configurações salvas com sucesso");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PROPOSAL] Erro ao salvar configurações");
        }
    }
    
    private void LoadSettings()
    {
        try
        {
            var settings = Properties.Settings.Default;
            
            // Verificar se deve carregar configurações salvas
            if (settings.ProposalSaveConfiguration)
            {
                // Carregar configurações do Proposal
                var savedStakeText = settings.ProposalStakeText ?? "0.35";
                
                // CORREÇÃO: Validar stake carregado - garantir que seja pelo menos 0.35
                if (decimal.TryParse(savedStakeText, out var savedStake) && savedStake >= 0.35m)
                {
                    StakeText = savedStakeText;
                    _logger.LogInformation("[PROPOSAL] Stake carregado das configurações: {SavedStake}", savedStake);
                }
                else
                {
                    StakeText = "0.35";
                    _logger.LogWarning("[PROPOSAL] Stake inválido nas configurações ({SavedStakeText}), usando padrão: 0.35", savedStakeText);
                }
                
                Duration = settings.ProposalDuration ?? "5";
                DurationType = settings.ProposalDurationType ?? "t";
                BarrierText = settings.ProposalBarrierText ?? "";
                IsContinuous = settings.ProposalIsContinuous;
                _saveConfiguration = settings.ProposalSaveConfiguration;
                
                // Garantir que o stake original seja o mesmo que o carregado
                _originalStake = _stake;
                
                OnPropertyChanged(nameof(SaveConfiguration));
                
                _logger.LogInformation("[PROPOSAL] Configurações carregadas com sucesso");
            }
            else
            {
                _logger.LogInformation("[PROPOSAL] Save Configuration desativado, usando valores padrão");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "[PROPOSAL] Erro ao carregar configurações, usando valores padrão");
        }
    }

    // IDisposable
    public void Dispose()
    {
        // Parar simulação automática se estiver ativa
        StopAutoSimulation();
        
        // Unsubscribe from events to prevent memory leaks
        if (_derivApiService != null)
        {
            _derivApiService.ProposalReceived -= OnProposalReceived;
            _derivApiService.TickReceived -= OnTickReceived;
            _derivApiService.ErrorOccurred -= OnErrorOccurred;
            _derivApiService.BuyCompleted -= OnBuyCompleted;
        }

        _proposalSemaphore?.Dispose();
        GC.SuppressFinalize(this);
    }
}

public class ContractIdUpdatedEventArgs : EventArgs
{
    public string OldProposalId { get; set; } = string.Empty;
    public string NewContractId { get; set; } = string.Empty; // Será o transaction_id da API
}

public class ContractSimulatedEventArgs : EventArgs
{
    public string ContractType { get; set; } = string.Empty;
    public string ContractId { get; set; } = string.Empty;
    public decimal StartSpot { get; set; }
    public decimal Stake { get; set; }
    public decimal Payout { get; set; }
    public int Duration { get; set; }
    public string DurationType { get; set; } = string.Empty;
}